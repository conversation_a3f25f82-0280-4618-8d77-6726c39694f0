{"FileVersion": 3, "Version": 72, "VersionName": "2.11.1", "FriendlyName": "Cesium for Unreal", "Description": "Unlock the 3D geospatial ecosystem in Unreal Engine with real-world 3D content and a high accuracy full-scale WGS84 globe.", "Category": "Geospatial", "CreatedBy": "Cesium GS, Inc.", "CreatedByURL": "https://cesium.com", "DocsURL": "https://cesium.com/learn/unreal/", "MarketplaceURL": "com.epicgames.launcher://ue/marketplace/content/87b0d05800a545d49bf858ef3458c4f7", "SupportURL": "https://community.cesium.com", "EngineVersion": "5.2.0", "CanContainContent": true, "Installed": true, "SupportedTargetPlatforms": ["Win64", "<PERSON>", "Linux", "Android", "IOS"], "Modules": [{"Name": "CesiumRuntime", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64", "<PERSON>", "Linux", "Android", "IOS"]}, {"Name": "CesiumEditor", "Type": "Editor", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64", "<PERSON>", "Linux", "Android", "IOS"]}], "Plugins": [{"Name": "SunPosition", "Enabled": true}, {"Name": "Water", "Enabled": true}]}