# UE 5.3+ defaults this to 11, which is not really high enough.
# 100 is probably high enough for reasonable values of Cesium3DTileset's 'MaximumSimultaneousTileLoads property,
# but if you get log messages like "Warning: Reached threaded request limit", and you're sure you want to use
# such a high value, you may need to increase this.
[HTTP.HttpThread]
RunningThreadedRequestLimit=100

# Set HttpThreadActiveFrameTimeInSeconds - Network download speed is very sensitive to this value
# Maps to the sleep time (ms) between every loop of our libcurl processing thread
# The value of 0.001 (or 1 ms) has the same performance as 0, yet still reserves some time for idle
#
# Set HttpMaxConnectionsPerServer - maps to libcurl's CURLMOPT_MAX_HOST_CONNECTIONS
# Unreal defaults to 16, but this is lower than a typical value for MaximumSimultaneousTileLoads
# Use a number just past the highest reasonable value we think a user should ever pick
[HTTP]
HttpThreadActiveFrameTimeInSeconds=0.001
HttpMaxConnectionsPerServer=40

[/Script/Engine.LocalPlayer]
AspectRatioAxisConstraint=AspectRatio_MaintainXFOV

[CoreRedirects]
+FunctionRedirects=(OldName="CesiumMetadataFeatureTableBlueprintLibrary.GetPropertiesForFeatureID",NewName="GetMetadataValuesForFeatureID")

+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateTransformLongitudeLatitudeHeightToUe",NewName="InaccurateTransformLongitudeLatitudeHeightToUnreal")

+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateTransformUeToLongitudeLatitudeHeight",NewName="InaccurateTransformUnrealToLongitudeLatitudeHeight")
+PropertyRedirects=(OldName="CesiumGeoreference.InaccurateTransformUnrealToLongitudeLatitudeHeight.Ue", NewName="Unreal")

+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateTransformEcefToUe",NewName="InaccurateTransformEcefToUnreal")

+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateTransformUeToEcef",NewName="InaccurateTransformUnrealToEcef")
+PropertyRedirects=(OldName="CesiumGeoreference.InaccurateTransformUnrealToEcef.Ue", NewName="Unreal")

+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateTransformRotatorUeToEnu",NewName="InaccurateTransformRotatorUnrealToEastNorthUp")
+PropertyRedirects=(OldName="CesiumGeoreference.InaccurateTransformRotatorUnrealToEastNorthUp.UeRotator", NewName="UnrealRotator")
+PropertyRedirects=(OldName="CesiumGeoreference.InaccurateTransformRotatorUnrealToEastNorthUp.UeLocation", NewName="UnrealLocation")

+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateTransformRotatorEnuToUe",NewName="InaccurateTransformRotatorEastNorthUpToUnreal")
+PropertyRedirects=(OldName="CesiumGeoreference.InaccurateTransformRotatorEastNorthUpToUnreal.UeLocation", NewName="UnrealLocation")

+PropertyRedirects=(OldName="CesiumGeoreference.InaccurateComputeEastNorthUpToUnreal.Ue", NewName="Unreal")

+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateSetGeoreferenceOrigin",NewName="SetOriginLongitudeLatitudeHeight")

+ClassRedirects=(OldName="CesiumGeoreferenceComponent", NewName="CesiumGlobeAnchorComponent")
+ClassRedirects=(OldName="CesiumSunSky_C",NewName="/Script/CesiumRuntime.CesiumSunSky",OverrideClassName="/Script/CoreUObject.Class")

+PropertyRedirects=(OldName="CesiumSunSky.EnableMobileRendering",NewName="UseMobileRendering")

+FunctionRedirects=(OldName="CesiumSunSky.AdjustAtmosphereRadius",NewName="UpdateAtmosphereRadius")

+PropertyRedirects=(OldName="CesiumGeoreference.WorldOriginCamera",NewName="SubLevelCamera")

# Remove "Inaccurate" from all the function names.
+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateGetGeoreferenceOriginLongitudeLatitudeHeight",NewName="GetOriginLongitudeLatitudeHeight")
+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateSetGeoreferenceOriginLongitudeLatitudeHeight",NewName="SetOriginLongitudeLatitudeHeight")
+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateSetGeoreferenceOriginEcef",NewName="SetOriginEarthCenteredEarthFixed")
+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateTransformLongitudeLatitudeHeightToEcef",NewName="TransformLongitudeLatitudeHeightToEcef")
+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateTransformEcefToLongitudeLatitudeHeight",NewName="TransformEcefToLongitudeLatitudeHeight")
+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateTransformLongitudeLatitudeHeightToUnreal",NewName="TransformLongitudeLatitudeHeightPositionToUnreal")
+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateTransformUnrealToLongitudeLatitudeHeight",NewName="TransformUnrealPositionToLongitudeLatitudeHeight")
+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateTransformEcefToUnreal",NewName="TransformEarthCenteredEarthFixedPositionToUnreal")
+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateTransformUnrealToEcef",NewName="TransformUnrealPositionToEarthCenteredEarthFixed")
+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateTransformRotatorUnrealToEastNorthUp",NewName="TransformRotatorUnrealToEastNorthUp")
+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateTransformRotatorEastNorthUpToUnreal",NewName="TransformRotatorEastNorthUpToUnreal")
+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateComputeEastNorthUpToUnreal",NewName="ComputeEastNorthUpToUnreal")
+FunctionRedirects=(OldName="CesiumGeoreference.InaccurateComputeEastNorthUpToEcef",NewName="ComputeEastNorthUpToEcef")
+FunctionRedirects=(OldName="CesiumGlobeAnchorComponent.InaccurateGetECEF",NewName="GetEarthCenteredEarthFixedPosition")
+FunctionRedirects=(OldName="CesiumGlobeAnchorComponent.InaccurateMoveToECEF",NewName="MoveToEarthCenteredEarthFixedPosition")
+FunctionRedirects=(OldName="CesiumGlobeAnchorComponent.InaccurateGetLongitudeLatitudeHeight",NewName="GetLongitudeLatitudeHeight")
+FunctionRedirects=(OldName="CesiumGlobeAnchorComponent.InaccurateMoveToLongitudeLatitudeHeight",NewName="MoveToLongitudeLatitudeHeight")
+FunctionRedirects=(OldName="GlobeAwareDefaultPawn.InaccurateFlyToLocationECEF",NewName="FlyToLocationECEF")
+FunctionRedirects=(OldName="GlobeAwareDefaultPawn.InaccurateFlyToLocationLongitudeLatitudeHeight",NewName="FlyToLocationLongitudeLatitudeHeight")

+PropertyRedirects=(OldName="CesiumPolygonRasterOverlay.ExcludeTilesInside",NewName="ExcludeSelectedTiles")

+FunctionRedirects=(OldName="CesiumGeoreference.TransformUnrealToLongitudeLatitudeHeight",NewName="TransformUnrealPositionToLongitudeLatitudeHeight")
+PropertyRedirects=(OldName="CesiumGeoreference.TransformUnrealPositionToLongitudeLatitudeHeight.Unreal", NewName="UnrealPosition")
+FunctionRedirects=(OldName="CesiumGeoreference.TransformEcefToUnreal",NewName="TransformEarthCenteredEarthFixedPositionToUnreal")
+PropertyRedirects=(OldName="CesiumGeoreference.TransformEarthCenteredEarthFixedPositionToUnreal.Ecef", NewName="EarthCenteredEarthFixedPosition")
+FunctionRedirects=(OldName="CesiumGeoreference.TransformUnrealToEcef",NewName="TransformUnrealPositionToEarthCenteredEarthFixed")
+PropertyRedirects=(OldName="CesiumGeoreference.TransformUnrealPositionToEarthCenteredEarthFixed.Unreal", NewName="UnrealPosition")
+FunctionRedirects=(OldName="CesiumGeoreference.TransformLongitudeLatitudeHeightToUnreal",NewName="TransformLongitudeLatitudeHeightPositionToUnreal")
+FunctionRedirects=(OldName="CesiumGeoreference.TransformRotatorUnrealToEastSouthUp",NewName="TransformUnrealRotatorToEastSouthUp")
+FunctionRedirects=(OldName="CesiumGeoreference.TransformRotatorEastSouthUpToUnreal",NewName="TransformEastSouthUpRotatorToUnreal")
+PropertyRedirects=(OldName="CesiumGeoreference.TransformEastSouthUpRotatorToUnreal.EsuRotator", NewName="EastSouthUpRotator")
+FunctionRedirects=(OldName="CesiumGeoreference.ComputeEastSouthUpToUnreal",NewName="ComputeEastSouthUpToUnrealTransformation")
+PropertyRedirects=(OldName="CesiumGeoreference.ComputeEastSouthUpToUnrealTransformation.Unreal", NewName="UnrealLocation")
+FunctionRedirects=(OldName="CesiumGeoreference.SetGeoreferenceOriginLongitudeLatitudeHeight",NewName="SetOriginLongitudeLatitudeHeight")
+FunctionRedirects=(OldName="CesiumGeoreference.GetGeoreferenceOriginLongitudeLatitudeHeight",NewName="GetOriginLongitudeLatitudeHeight")
+FunctionRedirects=(OldName="CesiumGeoreference.SetGeoreferenceOriginEcef",NewName="SetOriginEarthCenteredEarthFixed")
+PropertyRedirects=(OldName="CesiumGeoreference.SetOriginEarthCenteredEarthFixed.TargetEcef", NewName="TargetEarthCenteredEarthFixed")
+FunctionRedirects=(OldName="CesiumGlobeAnchorComponent.GetECEF",NewName="GetEarthCenteredEarthFixedPosition")
+FunctionRedirects=(OldName="CesiumGlobeAnchorComponent.MoveToECEF",NewName="MoveToEarthCenteredEarthFixedPosition")
+PropertyRedirects=(OldName="CesiumGlobeAnchorComponent.GetEarthCenteredEarthFixedPosition.TargetEcef", NewName="EarthCenteredEarthFixedPosition")
+PropertyRedirects=(OldName="CesiumGlobeAnchorComponent.MoveToLongitudeLatitudeHeight.TargetLongitudeLatitudeHeight", NewName="LongitudeLatitudeHeight")

# EXT_feature_metadata -> EXT_structural_metadata changes

# Deprecate the old type enum. Unfortunately, there's no way to redirect it to a CesiumMetadataValueType struct.
+EnumRedirects=(OldName="ECesiumMetadataTrueType", NewName="ECesiumMetadataTrueType_DEPRECATED", ValueChanges=(("None","None_DEPRECATED"),("Int8","Int8_DEPRECATED"),("Uint8","Uint8_DEPRECATED"),("Int16","Int16_DEPRECATED"),("Uint16","Uint16_DEPRECATED"),("Int32","Int32_DEPRECATED"),("Uint32","Uint32_DEPRECATED"),("Int64","Int64_DEPRECATED"),("Uint64","Uint64_DEPRECATED"),("Float32","Float32_DEPRECATED"),("Float64","Float64_DEPRECATED"),("Boolean","Boolean_DEPRECATED"),("Enum","Enum_DEPRECATED"),("String","String_DEPRECATED"),("Array","Array_DEPRECATED")))

+StructRedirects=(OldName="CesiumMetadataGenericValue", NewName="CesiumMetadataValue")
+ClassRedirects=(OldName="CesiumMetadataGenericValueBlueprintLibrary", NewName="CesiumMetadataValueBlueprintLibrary")
+FunctionRedirects=(OldName="CesiumMetadataValueBlueprintLibrary.GetBlueprintComponentType",NewName="CesiumMetadataValueBlueprintLibrary.GetArrayElementBlueprintType")

+StructRedirects=(OldName="CesiumMetadataArray", NewName="CesiumPropertyArray")
+ClassRedirects=(OldName="CesiumMetadataArrayBlueprintLibrary", NewName="CesiumPropertyArrayBlueprintLibrary")
+FunctionRedirects=(OldName="CesiumPropertyArrayBlueprintLibrary.GetBlueprintComponentType",NewName="CesiumPropertyArrayBlueprintLibrary.GetElementBlueprintType")
+FunctionRedirects=(OldName="CesiumPropertyArrayBlueprintLibrary.GetSize", NewName="CesiumPropertyArrayBlueprintLibrary.GetArraySize")

+StructRedirects=(OldName="CesiumFeatureTable", NewName="CesiumPropertyTable")
+ClassRedirects=(OldName="CesiumFeatureTableBlueprintLibrary", NewName="CesiumPropertyTableBlueprintLibrary")

+FunctionRedirects=(OldName="CesiumPropertyTableBlueprintLibrary.GetNumberOfFeatures", NewName="GetPropertyTableCount")
+PropertyRedirects=(OldName="CesiumPropertyTableBlueprintLibrary.GetPropertyTableCount.FeatureTable", NewName="PropertyTable")

+FunctionRedirects=(OldName="CesiumPropertyTableBlueprintLibrary.GetMetadataValuesForFeatureID", NewName="GetMetadataValuesForFeature")
+PropertyRedirects=(OldName="CesiumPropertyTableBlueprintLibrary.GetMetadataValuesForFeature.FeatureTable", NewName="PropertyTable")

+FunctionRedirects=(OldName="CesiumPropertyTableBlueprintLibrary.GetMetadataValuesAsStringForFeatureID", NewName="GetMetadataValuesForFeatureAsStrings")
+PropertyRedirects=(OldName="CesiumPropertyTableBlueprintLibrary.GetMetadataValuesForFeatureAsStrings.FeatureTable", NewName="PropertyTable")

+PropertyRedirects=(OldName="CesiumPropertyTableBlueprintLibrary.GetProperties.FeatureTable", NewName="PropertyTable")

+StructRedirects=(OldName="CesiumMetadataProperty", NewName="CesiumPropertyTableProperty")
+ClassRedirects=(OldName="CesiumMetadataPropertyBlueprintLibrary", NewName="CesiumPropertyTablePropertyBlueprintLibrary")
+FunctionRedirects=(OldName="CesiumPropertyTablePropertyBlueprintLibrary.GetBlueprintComponentType", NewName="CesiumPropertyTablePropertyBlueprintLibrary.GetArrayElementBlueprintType")
+FunctionRedirects=(OldName="CesiumPropertyTablePropertyBlueprintLibrary.GetGenericValue", NewName="CesiumPropertyTablePropertyBlueprintLibrary.GetValue")
+FunctionRedirects=(OldName="CesiumPropertyTablePropertyBlueprintLibrary.GetNumberOfFeatures", NewName="CesiumPropertyTablePropertyBlueprintLibrary.GetPropertySize")
+FunctionRedirects=(OldName="CesiumPropertyTablePropertyBlueprintLibrary.GetComponentCount", NewName="CesiumPropertyTablePropertyBlueprintLibrary.GetArraySize")

+StructRedirects=(OldName="CesiumFeatureTexture", NewName="CesiumPropertyTexture")
+StructRedirects=(OldName="CesiumFeatureTextureProperty", NewName="CesiumPropertyTextureProperty")
+FunctionRedirects=(OldName="CesiumPropertyTexturePropertyBlueprintLibrary.GetPropertyKeys", NewName="CesiumPropertyTexturePropertyBlueprintLibrary.GetPropertyNames")
+FunctionRedirects=(OldName="CesiumPropertyTexturePropertyBlueprintLibrary.GetTextureCoordinateIndex", NewName="CesiumPropertyTexturePropertyBlueprintLibrary.GetUnrealUVChannel")

+StructRedirects=(OldName="CesiumMetadataModel", NewName="CesiumModelMetadata")
+ClassRedirects=(OldName="CesiumMetadataModelBlueprintLibrary", NewName="CesiumModelMetadataBlueprintLibrary")
+PropertyRedirects=(OldName="CesiumModelMetadataBlueprintLibrary.GetFeatureTables.MetadataModel", NewName="ModelMetadata")
+PropertyRedirects=(OldName="CesiumModelMetadataBlueprintLibrary.GetFeatureTextures.MetadataModel", NewName="ModelMetadata")

+FunctionRedirects=(OldName="CesiumMetadataUtilityBlueprintLibrary.GetMetadataModel", NewName="CesiumModelMetadataBlueprintLibrary.GetModelMetadata")

+EnumRedirects=(OldName="ECesiumPropertyComponentType", NewName="ECesiumPropertyComponentType_DEPRECATED", ValueChanges=(("Uint8","Uint8_DEPRECATED"),("Float","Float_DEPRECATED")))

+EnumRedirects=(OldName="ECesiumPropertyType", NewName="ECesiumPropertyType_DEPRECATED", ValueChanges=(("Scalar","Scalar_DEPRECATED"),("Vec2","Vec2_DEPRECATED"),("Vec3","Vec3_DEPRECATED"),("Vec4","Vec4_DEPRECATED")))

+EnumRedirects=(OldName="ECesiumFeatureTableAccessType", NewName="ECesiumFeatureTableAccessType_DEPRECATED", ValueChanges=(("Unknown","Unknown_DEPRECATED"),("Texture","Texture_DEPRECATED"),("Attribute","Attribute_DEPRECATED"),("Mixed","Mixed_DEPRECATED")))

+EnumRedirects=(OldName="ECesiumMetadataPackedGpuType", NewName="ECesiumMetadataPackedGpuType_DEPRECATED", ValueChanges=(("None","Unknown_DEPRECATED"),("Uint8","Uint8_DEPRECATED"),("Float","Float_DEPRECATED")))

+FunctionRedirects=(OldName="CesiumFeatureIdTextureBlueprintLibrary.GetTextureCoordinateIndex", NewName="CesiumFeatureIdTextureBlueprintLibrary.GetUnrealUVChannel")

+PropertyRedirects=(OldName="CesiumWebMapTileServiceRasterOverlay.Url", NewName="CesiumWebMapTileServiceRasterOverlay.BaseUrl")
+PropertyRedirects=(OldName="CesiumWebMapTileServiceRasterOverlay.West", NewName="CesiumWebMapTileServiceRasterOverlay.RectangleWest")
+PropertyRedirects=(OldName="CesiumWebMapTileServiceRasterOverlay.South", NewName="CesiumWebMapTileServiceRasterOverlay.RectangleSouth")
+PropertyRedirects=(OldName="CesiumWebMapTileServiceRasterOverlay.East", NewName="CesiumWebMapTileServiceRasterOverlay.RectangleEast")
+PropertyRedirects=(OldName="CesiumWebMapTileServiceRasterOverlay.North", NewName="CesiumWebMapTileServiceRasterOverlay.RectangleNorth")
+PropertyRedirects=(OldName="CesiumWebMapTileServiceRasterOverlay.UseWebMercatorProjection", NewName="CesiumWebMapTileServiceRasterOverlay.UseWebMercatorProjection_DEPRECATED")