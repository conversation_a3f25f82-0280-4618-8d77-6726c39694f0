# GeoJSON 简化数组输出功能使用指南

## 🎯 功能概述

新增的 `ParseGeoJsonToSimpleArray` 函数可以将GeoJSON数据解析为一个简化的数组结构，每个数组元素包含：
- 三个自定义属性值
- 一个扁平化的坐标数组（包含所有坐标点）

## 📊 数据结构

### 简化的数据项结构
```cpp
USTRUCT(BlueprintType)
struct FSimpleGeoJsonItem
{
    GENERATED_BODY()

    // 三个自定义属性值
    UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
    FString Property1;  // 第一个属性值

    UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
    FString Property2;  // 第二个属性值

    UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
    FString Property3;  // 第三个属性值

    // 扁平化的坐标数组 - 包含所有多边形的所有环的所有点
    UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
    TArray<FVector2D> Coordinates;  // 所有坐标点的扁平数组
};
```

## 🚀 使用方法

### C++中使用

```cpp
// 读取您的GeoJSON文件内容
FString GeoJsonContent = TEXT("您的FeatureCollection内容");

// 调用简化数组解析函数
TArray<FSimpleGeoJsonItem> SimpleArray = AYunnToPolygon::ParseGeoJsonToSimpleArray(
    GeoJsonContent,
    TEXT("owner"),           // 第一个属性键
    TEXT("remark"),          // 第二个属性键
    TEXT("serial_number")    // 第三个属性键
);

// 遍历所有项目
for (int32 i = 0; i < SimpleArray.Num(); i++)
{
    const auto& Item = SimpleArray[i];
    
    // 获取属性值
    FString Owner = Item.Property1;
    FString Type = Item.Property2;
    FString SerialNumber = Item.Property3;
    
    UE_LOG(LogTemp, Log, TEXT("项目 %d: %s (%s) - 序列号: %s"), 
        i, *Owner, *Type, *SerialNumber);
    
    // 获取所有坐标点
    TArray<FVector2D> AllCoordinates = Item.Coordinates;
    UE_LOG(LogTemp, Log, TEXT("  坐标点总数: %d"), AllCoordinates.Num());
    
    // 处理每个坐标点
    for (const FVector2D& Point : AllCoordinates)
    {
        // Point.X = 经度, Point.Y = 纬度
        // 在这里处理坐标点，例如创建3D网格、标记位置等
    }
}
```

### 蓝图中使用

1. 搜索并添加 **"Parse Geo Json To Simple Array"** 节点
2. 输入GeoJSON字符串和三个属性键名
3. 从返回的 `TArray<FSimpleGeoJsonItem>` 中获取数据
4. 遍历数组，每个元素包含属性值和坐标数组

## 📋 实际应用示例

### 土地所有权数据处理

```cpp
// 解析土地所有权数据
TArray<FSimpleGeoJsonItem> LandData = AYunnToPolygon::ParseGeoJsonToSimpleArray(
    LandOwnershipGeoJson,
    TEXT("owner"),              // 土地所有者
    TEXT("remark"),             // 土地类型（集体/国有）
    TEXT("serial_number")       // 序列号
);

// 为每块土地创建3D可视化
for (int32 i = 0; i < LandData.Num(); i++)
{
    const auto& Land = LandData[i];
    
    // 创建土地标签
    FString LandLabel = FString::Printf(TEXT("%s (%s)"), *Land.Property1, *Land.Property2);
    
    // 使用坐标创建多边形网格
    CreateLandPolygonMesh(Land.Coordinates, LandLabel);
    
    // 根据土地类型设置不同颜色
    FLinearColor LandColor = Land.Property2 == TEXT("集体") ? 
        FLinearColor::Green : FLinearColor::Blue;
    SetLandColor(i, LandColor);
}
```

### 与现有ParseWKT函数的对比

```cpp
// 旧方式 - 使用ParseWKT（只能处理单个多边形）
TArray<FVector2D> SinglePolygon = UBPFunctionLibBPLibrary::ParseWKT(WKTString);

// 新方式 - 使用ParseGeoJsonToSimpleArray（可处理多个Feature）
TArray<FSimpleGeoJsonItem> MultipleFeatures = AYunnToPolygon::ParseGeoJsonToSimpleArray(
    GeoJsonString, Key1, Key2, Key3);

// 新方式的优势：
// 1. 支持多个Feature
// 2. 包含属性信息
// 3. 坐标格式与ParseWKT兼容
```

## 🔧 测试功能

### 运行测试
在蓝图中调用 **"Test Simple Geo Json Array Parsing"** 函数，查看输出日志验证功能。

### 预期输出示例
```
=== 开始测试简化数组格式GeoJSON解析 ===
简化数组解析完成，共获得 2 个项目
--- 项目 0 ---
  所有者: 广州市越秀区矿泉街瑶台村第四经济合作社农民集体
  类型: 集体
  序列号: 127
  坐标点总数: 47
    坐标 0: (113.247251, 23.156129)
    坐标 1: (113.247271, 23.156160)
    ... 还有 45 个坐标点
--- 项目 1 ---
  所有者: 广州市中心区交通项目领导小组办公室
  类型: 国有
  序列号: 113
  坐标点总数: 42
    坐标 0: (113.246369, 23.150295)
    坐标 1: (113.247545, 23.150545)
    ... 还有 40 个坐标点
=== 简化数组格式测试完成 ===
```

## ⚠️ 重要说明

### 坐标扁平化
- **所有多边形的所有环的所有点**都会被合并到一个坐标数组中
- 如果您需要区分不同的多边形或环，请使用 `ParseGeoJsonFeatureCollection` 函数
- 扁平化适合简单的可视化需求，如创建点云、边界线等

### 数据顺序
坐标点在数组中的顺序：
1. Feature 0 的 Polygon 0 的 Ring 0 的所有点
2. Feature 0 的 Polygon 0 的 Ring 1 的所有点（如果有内环）
3. Feature 0 的 Polygon 1 的 Ring 0 的所有点（如果有多个多边形）
4. 以此类推...

### 性能考虑
- 简化数组格式减少了数据结构的复杂性
- 适合需要快速访问所有坐标点的场景
- 内存使用相对较少

## 🎯 适用场景

✅ **适合使用简化数组的场景**:
- 创建简单的多边形可视化
- 计算边界框或中心点
- 生成点云数据
- 与现有的ParseWKT工作流集成

❌ **不适合使用简化数组的场景**:
- 需要区分多边形和环的结构
- 需要处理复杂的多边形拓扑
- 需要保持原始的GeoJSON层次结构

现在您可以轻松获得包含属性值和坐标数组的简化数据结构了！
