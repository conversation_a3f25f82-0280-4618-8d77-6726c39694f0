// Fill out your copyright notice in the Description page of Project Settings.

#include "YunntoGameInstance.h"

// 定义静态变量
TSharedPtr<SNotificationItem> UYunntoGameInstance::CurrentNotificationItem;
//#include "NetConnection/Public/crypt_unit.h"
/*
#include "Json/Public/JSON.h"
#include "Json/Public/Serialization/JsonSerializer.h"
#include "Json/Public/Serialization/JsonReader.h"*/
#include "BPFunctionLibBPLibrary.h"
#include <iostream>

#include "Misc/Base64.h"
#include "Math/UnrealMath.h"
#include "TimerManager.h"

#define PADDING_MODE_ISO7816_4 0
#define PADDING_MODE_PKCS7 1
#define PADDING_MODE_COUNT 2

#define P 0xffffffffffffffc5ull

// Constants are the integer part of the sines of integers (in radians) * 2^32.
static const uint32_t ka[64] = {
0xd76aa478, 0xe8c7b756, 0x242070db, 0xc1bd<PERSON><PERSON> ,
0xf57c0faf, 0x4787c62a, 0xa8304613, 0xfd469501 ,
0x698098d8, 0x8b44f7af, 0xffff5bb1, 0x895cd7be ,
0x6b901122, 0xfd987193, 0xa679438e, 0x49b40821 ,
0xf61e2562, 0xc040b340, 0x265e5a51, 0xe9b6c7aa ,
0xd62f105d, 0x02441453, 0xd8a1e681, 0xe7d3fbc8 ,
0x21e1cde6, 0xc33707d6, 0xf4d50d87, 0x455a14ed ,
0xa9e3e905, 0xfcefa3f8, 0x676f02d9, 0x8d2a4c8a ,
0xfffa3942, 0x8771f681, 0x6d9d6122, 0xfde5380c ,
0xa4beea44, 0x4bdecfa9, 0xf6bb4b60, 0xbebfbc70 ,
0x289b7ec6, 0xeaa127fa, 0xd4ef3085, 0x04881d05 ,
0xd9d4d039, 0xe6db99e5, 0x1fa27cf8, 0xc4ac5665 ,
0xf4292244, 0x432aff97, 0xab9423a7, 0xfc93a039 ,
0x655b59c3, 0x8f0ccc92, 0xffeff47d, 0x85845dd1 ,
0x6fa87e4f, 0xfe2ce6e0, 0xa3014314, 0x4e0811a1 ,
0xf7537e82, 0xbd3af235, 0x2ad7d2bb, 0xeb86d391 };

// r specifies the per-round shift amounts
static const uint32_t r[] = { 7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22,
					  5,  9, 14, 20, 5,  9, 14, 20, 5,  9, 14, 20, 5,  9, 14, 20,
					  4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23,
					  6, 10, 15, 21, 6, 10, 15, 21, 6, 10, 15, 21, 6, 10, 15, 21 };

static uint32_t SB1[64] = {
	0x01010400, 0x00000000, 0x00010000, 0x01010404,
	0x01010004, 0x00010404, 0x00000004, 0x00010000,
	0x00000400, 0x01010400, 0x01010404, 0x00000400,
	0x01000404, 0x01010004, 0x01000000, 0x00000004,
	0x00000404, 0x01000400, 0x01000400, 0x00010400,
	0x00010400, 0x01010000, 0x01010000, 0x01000404,
	0x00010004, 0x01000004, 0x01000004, 0x00010004,
	0x00000000, 0x00000404, 0x00010404, 0x01000000,
	0x00010000, 0x01010404, 0x00000004, 0x01010000,
	0x01010400, 0x01000000, 0x01000000, 0x00000400,
	0x01010004, 0x00010000, 0x00010400, 0x01000004,
	0x00000400, 0x00000004, 0x01000404, 0x00010404,
	0x01010404, 0x00010004, 0x01010000, 0x01000404,
	0x01000004, 0x00000404, 0x00010404, 0x01010400,
	0x00000404, 0x01000400, 0x01000400, 0x00000000,
	0x00010004, 0x00010400, 0x00000000, 0x01010004
};

static uint32_t SB2[64] = {
	0x80108020, 0x80008000, 0x00008000, 0x00108020,
	0x00100000, 0x00000020, 0x80100020, 0x80008020,
	0x80000020, 0x80108020, 0x80108000, 0x80000000,
	0x80008000, 0x00100000, 0x00000020, 0x80100020,
	0x00108000, 0x00100020, 0x80008020, 0x00000000,
	0x80000000, 0x00008000, 0x00108020, 0x80100000,
	0x00100020, 0x80000020, 0x00000000, 0x00108000,
	0x00008020, 0x80108000, 0x80100000, 0x00008020,
	0x00000000, 0x00108020, 0x80100020, 0x00100000,
	0x80008020, 0x80100000, 0x80108000, 0x00008000,
	0x80100000, 0x80008000, 0x00000020, 0x80108020,
	0x00108020, 0x00000020, 0x00008000, 0x80000000,
	0x00008020, 0x80108000, 0x00100000, 0x80000020,
	0x00100020, 0x80008020, 0x80000020, 0x00100020,
	0x00108000, 0x00000000, 0x80008000, 0x00008020,
	0x80000000, 0x80100020, 0x80108020, 0x00108000
};

static uint32_t SB3[64] = {
	0x00000208, 0x08020200, 0x00000000, 0x08020008,
	0x08000200, 0x00000000, 0x00020208, 0x08000200,
	0x00020008, 0x08000008, 0x08000008, 0x00020000,
	0x08020208, 0x00020008, 0x08020000, 0x00000208,
	0x08000000, 0x00000008, 0x08020200, 0x00000200,
	0x00020200, 0x08020000, 0x08020008, 0x00020208,
	0x08000208, 0x00020200, 0x00020000, 0x08000208,
	0x00000008, 0x08020208, 0x00000200, 0x08000000,
	0x08020200, 0x08000000, 0x00020008, 0x00000208,
	0x00020000, 0x08020200, 0x08000200, 0x00000000,
	0x00000200, 0x00020008, 0x08020208, 0x08000200,
	0x08000008, 0x00000200, 0x00000000, 0x08020008,
	0x08000208, 0x00020000, 0x08000000, 0x08020208,
	0x00000008, 0x00020208, 0x00020200, 0x08000008,
	0x08020000, 0x08000208, 0x00000208, 0x08020000,
	0x00020208, 0x00000008, 0x08020008, 0x00020200
};

static uint32_t SB4[64] = {
	0x00802001, 0x00002081, 0x00002081, 0x00000080,
	0x00802080, 0x00800081, 0x00800001, 0x00002001,
	0x00000000, 0x00802000, 0x00802000, 0x00802081,
	0x00000081, 0x00000000, 0x00800080, 0x00800001,
	0x00000001, 0x00002000, 0x00800000, 0x00802001,
	0x00000080, 0x00800000, 0x00002001, 0x00002080,
	0x00800081, 0x00000001, 0x00002080, 0x00800080,
	0x00002000, 0x00802080, 0x00802081, 0x00000081,
	0x00800080, 0x00800001, 0x00802000, 0x00802081,
	0x00000081, 0x00000000, 0x00000000, 0x00802000,
	0x00002080, 0x00800080, 0x00800081, 0x00000001,
	0x00802001, 0x00002081, 0x00002081, 0x00000080,
	0x00802081, 0x00000081, 0x00000001, 0x00002000,
	0x00800001, 0x00002001, 0x00802080, 0x00800081,
	0x00002001, 0x00002080, 0x00800000, 0x00802001,
	0x00000080, 0x00800000, 0x00002000, 0x00802080
};

static uint32_t SB5[64] = {
	0x00000100, 0x02080100, 0x02080000, 0x42000100,
	0x00080000, 0x00000100, 0x40000000, 0x02080000,
	0x40080100, 0x00080000, 0x02000100, 0x40080100,
	0x42000100, 0x42080000, 0x00080100, 0x40000000,
	0x02000000, 0x40080000, 0x40080000, 0x00000000,
	0x40000100, 0x42080100, 0x42080100, 0x02000100,
	0x42080000, 0x40000100, 0x00000000, 0x42000000,
	0x02080100, 0x02000000, 0x42000000, 0x00080100,
	0x00080000, 0x42000100, 0x00000100, 0x02000000,
	0x40000000, 0x02080000, 0x42000100, 0x40080100,
	0x02000100, 0x40000000, 0x42080000, 0x02080100,
	0x40080100, 0x00000100, 0x02000000, 0x42080000,
	0x42080100, 0x00080100, 0x42000000, 0x42080100,
	0x02080000, 0x00000000, 0x40080000, 0x42000000,
	0x00080100, 0x02000100, 0x40000100, 0x00080000,
	0x00000000, 0x40080000, 0x02080100, 0x40000100
};

static uint32_t SB6[64] = {
	0x20000010, 0x20400000, 0x00004000, 0x20404010,
	0x20400000, 0x00000010, 0x20404010, 0x00400000,
	0x20004000, 0x00404010, 0x00400000, 0x20000010,
	0x00400010, 0x20004000, 0x20000000, 0x00004010,
	0x00000000, 0x00400010, 0x20004010, 0x00004000,
	0x00404000, 0x20004010, 0x00000010, 0x20400010,
	0x20400010, 0x00000000, 0x00404010, 0x20404000,
	0x00004010, 0x00404000, 0x20404000, 0x20000000,
	0x20004000, 0x00000010, 0x20400010, 0x00404000,
	0x20404010, 0x00400000, 0x00004010, 0x20000010,
	0x00400000, 0x20004000, 0x20000000, 0x00004010,
	0x20000010, 0x20404010, 0x00404000, 0x20400000,
	0x00404010, 0x20404000, 0x00000000, 0x20400010,
	0x00000010, 0x00004000, 0x20400000, 0x00404010,
	0x00004000, 0x00400010, 0x20004010, 0x00000000,
	0x20404000, 0x20000000, 0x00400010, 0x20004010
};

static uint32_t SB7[64] = {
	0x00200000, 0x04200002, 0x04000802, 0x00000000,
	0x00000800, 0x04000802, 0x00200802, 0x04200800,
	0x04200802, 0x00200000, 0x00000000, 0x04000002,
	0x00000002, 0x04000000, 0x04200002, 0x00000802,
	0x04000800, 0x00200802, 0x00200002, 0x04000800,
	0x04000002, 0x04200000, 0x04200800, 0x00200002,
	0x04200000, 0x00000800, 0x00000802, 0x04200802,
	0x00200800, 0x00000002, 0x04000000, 0x00200800,
	0x04000000, 0x00200800, 0x00200000, 0x04000802,
	0x04000802, 0x04200002, 0x04200002, 0x00000002,
	0x00200002, 0x04000000, 0x04000800, 0x00200000,
	0x04200800, 0x00000802, 0x00200802, 0x04200800,
	0x00000802, 0x04000002, 0x04200802, 0x04200000,
	0x00200800, 0x00000000, 0x00000002, 0x04200802,
	0x00000000, 0x00200802, 0x04200000, 0x00000800,
	0x04000002, 0x04000800, 0x00000800, 0x00200002
};

static uint32_t SB8[64] = {
	0x10001040, 0x00001000, 0x00040000, 0x10041040,
	0x10000000, 0x10001040, 0x00000040, 0x10000000,
	0x00040040, 0x10040000, 0x10041040, 0x00041000,
	0x10041000, 0x00041040, 0x00001000, 0x00000040,
	0x10040000, 0x10000040, 0x10001000, 0x00001040,
	0x00041000, 0x00040040, 0x10040040, 0x10041000,
	0x00001040, 0x00000000, 0x00000000, 0x10040040,
	0x10000040, 0x10001000, 0x00041040, 0x00040000,
	0x00041040, 0x00040000, 0x10041000, 0x00001000,
	0x00000040, 0x10040040, 0x00001000, 0x00041040,
	0x10001000, 0x00000040, 0x10000040, 0x10040000,
	0x10040040, 0x10000000, 0x00040000, 0x10001040,
	0x00000000, 0x10041040, 0x00040040, 0x10000040,
	0x10040000, 0x10001000, 0x10001040, 0x00000000,
	0x10041040, 0x00041000, 0x00041000, 0x00001040,
	0x00001040, 0x00040040, 0x10000000, 0x10041000
};


static uint32_t LHs[16] = {
	0x00000000, 0x00000001, 0x00000100, 0x00000101,
	0x00010000, 0x00010001, 0x00010100, 0x00010101,
	0x01000000, 0x01000001, 0x01000100, 0x01000101,
	0x01010000, 0x01010001, 0x01010100, 0x01010101
};

static uint32_t RHs[16] = {
	0x00000000, 0x01000000, 0x00010000, 0x01010000,
	0x00000100, 0x01000100, 0x00010100, 0x01010100,
	0x00000001, 0x01000001, 0x00010001, 0x01010001,
	0x00000101, 0x01000101, 0x00010101, 0x01010101,
};

static void
padding_add_iso7816_4(uint8_t buf[8], int offset) {
	buf[offset] = 0x80;
	memset(buf + offset + 1, 0, 7 - offset);
}

static void
padding_add_pkcs7(uint8_t buf[8], int offset) {
	uint8_t x = 8 - offset;
	memset(buf + offset, x, 8 - offset);
}

static int
padding_remove_iso7816_4(const uint8_t* last) {
	int padding = 1;
	int i;
	for (i = 0; i < 8; i++, last--) {
		if (*last == 0) {
			padding++;
		}
		else if (*last == 0x80) {
			return padding;
		}
		else {
			break;
		}
	}
	// invalid
	return 0;
}

static int
padding_remove_pkcs7(const uint8_t* last) {
	int padding = *last;
	int i;
	for (i = 1; i < padding; i++) {
		--last;
		if (*last != padding)
			return 0;	// invalid
	}
	return padding;
}

typedef void (*padding_add)(uint8_t buf[8], int offset);
typedef int (*padding_remove)(const uint8_t* last);



#define DES_FP(X,Y)											 \
{															   \
	X = ((X << 31) | (X >> 1)) & 0xFFFFFFFF;					\
	T = (X ^ Y) & 0xAAAAAAAA; X ^= T; Y ^= T;				   \
	Y = ((Y << 31) | (Y >> 1)) & 0xFFFFFFFF;					\
	T = ((Y >>  8) ^ X) & 0x00FF00FF; X ^= T; Y ^= (T <<  8);   \
	T = ((Y >>  2) ^ X) & 0x33333333; X ^= T; Y ^= (T <<  2);   \
	T = ((X >> 16) ^ Y) & 0x0000FFFF; Y ^= T; X ^= (T << 16);   \
	T = ((X >>  4) ^ Y) & 0x0F0F0F0F; Y ^= T; X ^= (T <<  4);   \
}

#define DES_ROUND(X,Y)						  \
{											   \
	T = *SK++ ^ X;							  \
	Y ^= SB8[ (T	  ) & 0x3F ] ^			  \
		 SB6[ (T >>  8) & 0x3F ] ^			  \
		 SB4[ (T >> 16) & 0x3F ] ^			  \
		 SB2[ (T >> 24) & 0x3F ];			   \
												\
	T = *SK++ ^ ((X << 28) | (X >> 4));		 \
	Y ^= SB7[ (T	  ) & 0x3F ] ^			  \
		 SB5[ (T >>  8) & 0x3F ] ^			  \
		 SB3[ (T >> 16) & 0x3F ] ^			  \
		 SB1[ (T >> 24) & 0x3F ];			   \
}

#define PUT_UINT32(n,b,i)					   \
{											   \
	(b)[(i)	] = (uint8_t) ( (n) >> 24 );	   \
	(b)[(i) + 1] = (uint8_t) ( (n) >> 16 );	   \
	(b)[(i) + 2] = (uint8_t) ( (n) >>  8 );	   \
	(b)[(i) + 3] = (uint8_t) ( (n)	   );	   \
}

#define GET_UINT32(n,b,i)					   \
{											   \
	(n) = ( (uint32_t) (b)[(i)	] << 24 )	   \
		| ( (uint32_t) (b)[(i) + 1] << 16 )	   \
		| ( (uint32_t) (b)[(i) + 2] <<  8 )	   \
		| ( (uint32_t) (b)[(i) + 3]	   );	  \
}

#define DES_IP(X,Y)											 \
{															   \
	T = ((X >>  4) ^ Y) & 0x0F0F0F0F; Y ^= T; X ^= (T <<  4);   \
	T = ((X >> 16) ^ Y) & 0x0000FFFF; Y ^= T; X ^= (T << 16);   \
	T = ((Y >>  2) ^ X) & 0x33333333; X ^= T; Y ^= (T <<  2);   \
	T = ((Y >>  8) ^ X) & 0x00FF00FF; X ^= T; Y ^= (T <<  8);   \
	Y = ((Y << 1) | (Y >> 31)) & 0xFFFFFFFF;					\
	T = (X ^ Y) & 0xAAAAAAAA; Y ^= T; X ^= T;				   \
	X = ((X << 1) | (X >> 31)) & 0xFFFFFFFF;					\
}

static padding_add padding_add_func[] = {
	padding_add_iso7816_4,
	padding_add_pkcs7,
};

static padding_remove padding_remove_func[] = {
	padding_remove_iso7816_4,
	padding_remove_pkcs7,
};

static inline bool
check_padding_mode(int mode) {
	if (mode < 0 || mode >= PADDING_MODE_COUNT) {
		return false;
	}
	return true;
}
static bool
add_padding(uint8_t buf[8], const uint8_t* src, int offset, int mode) {
	if (!check_padding_mode(mode)) {
		return false;
	}
	if (offset >= 8) {
		return false;
	}

	memcpy(buf, src, offset);
	padding_add_func[mode](buf, offset);

	return true;
}

static void
des_main_ks(uint32_t SK[32], const uint8_t key[8]) {
	int i;
	uint32_t X, Y, T;

	GET_UINT32(X, key, 0);
	GET_UINT32(Y, key, 4);

	/* Permuted Choice 1 */

	T = ((Y >> 4) ^ X) & 0x0F0F0F0F;  X ^= T; Y ^= (T << 4);
	T = ((Y) ^ X) & 0x10101010;  X ^= T; Y ^= (T);

	X = (LHs[(X) & 0xF] << 3) | (LHs[(X >> 8) & 0xF] << 2)
		| (LHs[(X >> 16) & 0xF] << 1) | (LHs[(X >> 24) & 0xF])
		| (LHs[(X >> 5) & 0xF] << 7) | (LHs[(X >> 13) & 0xF] << 6)
		| (LHs[(X >> 21) & 0xF] << 5) | (LHs[(X >> 29) & 0xF] << 4);

	Y = (RHs[(Y >> 1) & 0xF] << 3) | (RHs[(Y >> 9) & 0xF] << 2)
		| (RHs[(Y >> 17) & 0xF] << 1) | (RHs[(Y >> 25) & 0xF])
		| (RHs[(Y >> 4) & 0xF] << 7) | (RHs[(Y >> 12) & 0xF] << 6)
		| (RHs[(Y >> 20) & 0xF] << 5) | (RHs[(Y >> 28) & 0xF] << 4);

	X &= 0x0FFFFFFF;
	Y &= 0x0FFFFFFF;

	/* calculate subkeys */

	for (i = 0; i < 16; i++)
	{
		if (i < 2 || i == 8 || i == 15)
		{
			X = ((X << 1) | (X >> 27)) & 0x0FFFFFFF;
			Y = ((Y << 1) | (Y >> 27)) & 0x0FFFFFFF;
		}
		else
		{
			X = ((X << 2) | (X >> 26)) & 0x0FFFFFFF;
			Y = ((Y << 2) | (Y >> 26)) & 0x0FFFFFFF;
		}

		*SK++ = ((X << 4) & 0x24000000) | ((X << 28) & 0x10000000)
			| ((X << 14) & 0x08000000) | ((X << 18) & 0x02080000)
			| ((X << 6) & 0x01000000) | ((X << 9) & 0x00200000)
			| ((X >> 1) & 0x00100000) | ((X << 10) & 0x00040000)
			| ((X << 2) & 0x00020000) | ((X >> 10) & 0x00010000)
			| ((Y >> 13) & 0x00002000) | ((Y >> 4) & 0x00001000)
			| ((Y << 6) & 0x00000800) | ((Y >> 1) & 0x00000400)
			| ((Y >> 14) & 0x00000200) | ((Y) & 0x00000100)
			| ((Y >> 5) & 0x00000020) | ((Y >> 10) & 0x00000010)
			| ((Y >> 3) & 0x00000008) | ((Y >> 18) & 0x00000004)
			| ((Y >> 26) & 0x00000002) | ((Y >> 24) & 0x00000001);

		*SK++ = ((X << 15) & 0x20000000) | ((X << 17) & 0x10000000)
			| ((X << 10) & 0x08000000) | ((X << 22) & 0x04000000)
			| ((X >> 2) & 0x02000000) | ((X << 1) & 0x01000000)
			| ((X << 16) & 0x00200000) | ((X << 11) & 0x00100000)
			| ((X << 3) & 0x00080000) | ((X >> 6) & 0x00040000)
			| ((X << 15) & 0x00020000) | ((X >> 4) & 0x00010000)
			| ((Y >> 2) & 0x00002000) | ((Y << 8) & 0x00001000)
			| ((Y >> 14) & 0x00000808) | ((Y >> 9) & 0x00000400)
			| ((Y) & 0x00000200) | ((Y << 7) & 0x00000100)
			| ((Y >> 7) & 0x00000020) | ((Y >> 3) & 0x00000011)
			| ((Y << 2) & 0x00000004) | ((Y >> 21) & 0x00000002);
	}
}

static bool des_key(const std::string& in, uint32_t SK[32]) {
	size_t keysz = in.length();
	const void* key = (void*)in.c_str();
	if (keysz != 8) {
		return false;
	}
	des_main_ks(SK, (const uint8_t*)key);
	return true;
}

static inline int
b64index(uint8_t c) {
	static const int decoding[] = { 62,-1,-1,-1,63,52,53,54,55,56,57,58,59,60,61,-1,-1,-1,-2,-1,-1,-1,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,-1,-1,-1,-1,-1,-1,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51 };
	int decoding_size = sizeof(decoding) / sizeof(decoding[0]);
	if (c < 43) {
		return -1;
	}
	c -= 43;
	if (c >= decoding_size)
		return -1;
	return decoding[c];
}

static void
des_crypt(const uint32_t SK[32], const uint8_t input[8], uint8_t output[8]) {
	uint32_t X, Y, T;

	GET_UINT32(X, input, 0);
	GET_UINT32(Y, input, 4);

	DES_IP(X, Y);

	DES_ROUND(Y, X);  DES_ROUND(X, Y);
	DES_ROUND(Y, X);  DES_ROUND(X, Y);
	DES_ROUND(Y, X);  DES_ROUND(X, Y);
	DES_ROUND(Y, X);  DES_ROUND(X, Y);
	DES_ROUND(Y, X);  DES_ROUND(X, Y);
	DES_ROUND(Y, X);  DES_ROUND(X, Y);
	DES_ROUND(Y, X);  DES_ROUND(X, Y);
	DES_ROUND(Y, X);  DES_ROUND(X, Y);

	DES_FP(Y, X);

	PUT_UINT32(Y, output, 0);
	PUT_UINT32(X, output, 4);
}

static int
remove_padding(const uint8_t* last, int mode) {
	check_padding_mode(mode);
	return padding_remove_func[mode](last);
}

static inline uint64_t mul_mod_p(uint64_t a, uint64_t b) {
	uint64_t m = 0;
	while (b) {
		if (b & 1) {
			uint64_t t = P - a;
			if (m >= t) {
				m -= t;
			}
			else {
				m += a;
			}
		}
		if (a >= P - a) {
			a = a * 2 - P;
		}
		else {
			a = a * 2;
		}
		b >>= 1;
	}
	return m;
}

static inline uint64_t pow_mod_p(uint64_t a, uint64_t b) {
	if (b == 1) {
		return a;
	}
	uint64_t t = pow_mod_p(a, b >> 1);
	t = mul_mod_p(t, t);
	if (b % 2) {
		t = mul_mod_p(t, a);
	}
	return t;
}

static uint64_t powmodp(uint64_t a, uint64_t b) {
	if (a > P)
		a %= P;
	return pow_mod_p(a, b);
}

static void
push64(std::string& out, uint64_t rd) {
	uint8_t tmp[8];
	tmp[0] = rd & 0xff;
	tmp[1] = (rd >> 8) & 0xff;
	tmp[2] = (rd >> 16) & 0xff;
	tmp[3] = (rd >> 24) & 0xff;
	tmp[4] = (rd >> 32) & 0xff;
	tmp[5] = (rd >> 40) & 0xff;
	tmp[6] = (rd >> 48) & 0xff;
	tmp[7] = (rd >> 56) & 0xff;

	out = std::string((char *)tmp, 8);
	/*char tmp_c[10];
	memset(tmp_c, 0, sizeof(10));
	memcpy(tmp_c, tmp, 8);*/

	//out = t_out;
}

/*static bool read64(const std::string& in1, const std::string& in2, uint64_t& xx, uint64_t& yy) {
	size_t sz = in1.length();
	const uint8_t* x = (const uint8_t*)in1.c_str();
	if (sz != 8) {
		return false;
	}
	const uint8_t* y = (const uint8_t*)in2.c_str();
	if (sz != 8) {
		return false;
	}
	uint32_t u_x_0 = x[0] | x[1] << 8 | x[2] << 16 | x[3] << 24;
	uint32_t u_x_1 = x[4] | x[5] << 8 | x[6] << 16 | x[7] << 24;

	xx = (uint64_t)u_x_0 | (uint64_t)u_x_1 << 32;

	uint32_t u_y_0 = x[0] | x[1] << 8 | x[2] << 16 | x[3] << 24;
	uint32_t u_y_1 = x[4] | x[5] << 8 | x[6] << 16 | x[7] << 24;

	yy = (uint64_t)u_y_0 | (uint64_t)u_y_1 << 32;

	return true;
}*/

static bool read64(const std::string& in1, const std::string& in2, uint32_t xx[2], uint32_t yy[2]) {
	size_t sz = in1.length();
	const uint8_t* x = (const uint8_t*)in1.c_str();
	if (sz != 8) {
		return false;
	}
	const uint8_t* y = (const uint8_t*)in2.c_str();
	if (sz != 8) {
		return false;
	}
	xx[0] = x[0] | x[1] << 8 | x[2] << 16 | x[3] << 24;
	xx[1] = x[4] | x[5] << 8 | x[6] << 16 | x[7] << 24;
	yy[0] = y[0] | y[1] << 8 | y[2] << 16 | y[3] << 24;
	yy[1] = y[4] | y[5] << 8 | y[6] << 16 | y[7] << 24;

	return true;
}

#define G 5

#define LEFTROTATE(x, c) (((x) << (c)) | ((x) >> (32 - (c))))

static void
digest_md5(uint32_t w[16], uint32_t result[4]) {
	uint32_t a, b, c, d, f, g, temp;
	int i;

	a = 0x67452301u;
	b = 0xefcdab89u;
	c = 0x98badcfeu;
	d = 0x10325476u;

	for (i = 0; i < 64; i++) {
		if (i < 16) {
			f = (b & c) | ((~b) & d);
			g = i;
		}
		else if (i < 32) {
			f = (d & b) | ((~d) & c);
			g = (5 * i + 1) % 16;
		}
		else if (i < 48) {
			f = b ^ c ^ d;
			g = (3 * i + 5) % 16;
		}
		else {
			f = c ^ (b | (~d));
			g = (7 * i) % 16;
		}

		temp = d;
		d = c;
		c = b;
		b = b + LEFTROTATE((a + f + ka[i] + w[g]), r[i]);
		a = temp;
	}

	result[0] = a;
	result[1] = b;
	result[2] = c;
	result[3] = d;
}

/*static void
hmac(uint64_t x, uint64_t y, uint32_t result[2]) {

	union MyUnion
	{
		uint64_t value;
		struct
		{
			uint32_t one_u32;
			uint32_t tow_u32;
		} bit;
	};

	MyUnion my_x, my_y;
	my_x.value = x;
	my_y.value = y;

	uint32_t w[16];
	uint32_t rd[4];
	int i;
	for (i = 0; i < 16; i += 4) {
		w[i] = x >> 32;
		w[i + 1] = x & 0xffffffff;
		w[i + 2] = y >> 32;
		w[i + 3] = y & 0xffffffff;
	}

	digest_md5(w, rd);

	result[0] = rd[2] ^ rd[3];
	result[1] = rd[0] ^ rd[1];
}*/

static void
hmac(uint32_t x[2], uint32_t y[2], uint32_t result[2]) {
	uint32_t w[16];
	uint32_t rd[4];
	int i;
	for (i = 0; i < 16; i += 4) {
		w[i] = x[1];
		w[i + 1] = x[0];
		w[i + 2] = y[1];
		w[i + 3] = y[0];
	}

	digest_md5(w, rd);

	result[0] = rd[2] ^ rd[3];
	result[1] = rd[0] ^ rd[1];
}

static void
pushqword(std::string& out, uint32_t result[2]) {
	uint8_t tmp[8];
	tmp[0] = result[0] & 0xff;
	tmp[1] = (result[0] >> 8) & 0xff;
	tmp[2] = (result[0] >> 16) & 0xff;
	tmp[3] = (result[0] >> 24) & 0xff;
	tmp[4] = result[1] & 0xff;
	tmp[5] = (result[1] >> 8) & 0xff;
	tmp[6] = (result[1] >> 16) & 0xff;
	tmp[7] = (result[1] >> 24) & 0xff;

	out = std::string((char *)tmp,8);

	return;
}

namespace crypt_unit {

	std::string randomkey() {
		char tmp[10] = "";
		char* buf = tmp;

		int i;
		char x = 0;
		for (i = 0; i < 8; i++) {
			tmp[i] = rand() & 0xff;
			x ^= tmp[i];
		}

		if (x == 0) {
			tmp[0] |= 1;    // avoid 0
		}

		return std::string(buf,8);
	}

	std::string dhexchange(const std::string& in) {
		size_t sz = in.length();

		if (sz != 8) {
			return std::string();
		}

		const uint8_t* x = (const uint8_t*)in.c_str();


		uint32_t xx[2];
		xx[0] = x[0] | x[1] << 8 | x[2] << 16 | x[3] << 24;
		xx[1] = x[4] | x[5] << 8 | x[6] << 16 | x[7] << 24;

		uint64_t x64 = (uint64_t)xx[0] | (uint64_t)xx[1] << 32;

		uint64_t rd = powmodp(G, x64);
		std::string buffer;
		push64(buffer, rd);
		return buffer;
	}



	std::string dhsecret(const std::string& in1, const std::string& in2) {
		uint32_t x[2], y[2];

		read64(in1, in2, x, y);
		uint64_t xx = (uint64_t)x[0] | (uint64_t)x[1] << 32;
		uint64_t yy = (uint64_t)y[0] | (uint64_t)y[1] << 32;

		std::string buffer;
		if (xx == 0 || yy == 0) {

		}
		else {
			uint64_t rd = powmodp(xx, yy);
			push64(buffer, rd);
		}

		return buffer;
	}

	std::string hmac64(const std::string& in1, const std::string& in2) {
		uint32_t x[2], y[2];
		//uint64 x, y;
		read64(in1, in2, x, y);
		uint32_t result[2];
		hmac(x, y, result);
		std::string buffer;
		pushqword(buffer, result);
		return buffer;
	}

	std::string base64encode(const std::string& in) {
		static const char* encoding = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
		size_t sz = in.length();
		const uint8_t* text = (const uint8_t*)in.c_str();
		int encode_sz = (sz + 2) / 3 * 4;
		char tmp[256];
		char* buffer = tmp;
		if (encode_sz > 256) {
			//buffer = (char*)lua_newuserdatauv(L, encode_sz, 0);
			return std::string();
		}
		int i, j;
		j = 0;
		for (i = 0; i < (int)sz - 2; i += 3) {
			uint32_t v = text[i] << 16 | text[i + 1] << 8 | text[i + 2];
			buffer[j] = encoding[v >> 18];
			buffer[j + 1] = encoding[(v >> 12) & 0x3f];
			buffer[j + 2] = encoding[(v >> 6) & 0x3f];
			buffer[j + 3] = encoding[(v) & 0x3f];
			j += 4;
		}
		int padding = sz - i;
		uint32_t v;
		switch (padding) {
		case 1:
			v = text[i];
			buffer[j] = encoding[v >> 2];
			buffer[j + 1] = encoding[(v & 3) << 4];
			buffer[j + 2] = '=';
			buffer[j + 3] = '=';
			break;
		case 2:
			v = text[i] << 8 | text[i + 1];
			buffer[j] = encoding[v >> 10];
			buffer[j + 1] = encoding[(v >> 4) & 0x3f];
			buffer[j + 2] = encoding[(v & 0xf) << 2];
			buffer[j + 3] = '=';
			break;
		}
		std::string buf(buffer, encode_sz);
		return buf;
	}


	std::string base64decode(const std::string& in) {
		size_t sz = in.length();
		const uint8_t* text = (const uint8_t*)in.c_str();
		size_t decode_sz = (sz + 3) / 4 * 3;
		char tmp[256];
		char* buffer = tmp;
		if (decode_sz > 256) {
			//buffer = (char*)lua_newuserdatauv(L, decode_sz, 0);
			return std::string();
		}
		int i, j;
		int output = 0;
		for (i = 0; i < sz;) {
			int padding = 0;
			int c[4];
			for (j = 0; j < 4;) {
				if (i >= sz && 4 > j) {
					/*To improve compatibility, there may not be enough equal signs */
					c[j] = -2;
				}
				else {
					c[j] = b64index(text[i]);
				}
				if (c[j] == -1) {
					++i;
					continue;
				}
				if (c[j] == -2) {
					++padding;
				}
				++i;
				++j;
			}
			uint32_t v;
			switch (padding) {
			case 0:
				v = (unsigned)c[0] << 18 | c[1] << 12 | c[2] << 6 | c[3];
				buffer[output] = v >> 16;
				buffer[output + 1] = (v >> 8) & 0xff;
				buffer[output + 2] = v & 0xff;
				output += 3;
				break;
			case 1:
				if (c[3] != -2 || (c[2] & 3) != 0) {
					return std::string();
				}
				v = (unsigned)c[0] << 10 | c[1] << 4 | c[2] >> 2;
				buffer[output] = v >> 8;
				buffer[output + 1] = v & 0xff;
				output += 2;
				break;
			case 2:
				if (c[3] != -2 || c[2] != -2 || (c[1] & 0xf) != 0) {
					return std::string();
				}
				v = (unsigned)c[0] << 2 | c[1] >> 4;
				buffer[output] = v;
				++output;
				break;
			default:
				return std::string();
			}
		}

		
		return std::string(buffer, output);
	}

	std::string hexencode(const std::string& in) {
		static char hex[] = "0123456789abcdef";
		size_t sz = in.length();
		const uint8_t* text = (const uint8_t*)in.c_str();
		char tmp[256];
		memset(tmp, 0, 256);

		//std::string buffer;
		if (sz <= 256 / 2) {
			int i;
			for (i = 0; i < sz; i++) {
				tmp[i * 2] = hex[text[i] >> 4];
				tmp[i * 2 + 1] = hex[text[i] & 0xf];
			}
			//buffer = tmp;
		}
		std::string buffer = std::string(tmp, sz*2);
		return buffer;
	}

	//des 编码
	std::string desencode(const std::string& in1, const std::string& in2) {
		uint32_t SK[32];
		if (!des_key(in1, SK)) {
			return std::string();
		}

		size_t textsz = in2.size();
		const uint8_t* text = (const uint8_t*)in2.c_str();
		size_t chunksz = (textsz + 8) & ~7;
		int padding_mode = 0;
		uint8_t tmp[256];
		uint8_t* buffer = tmp;

		if (chunksz > 256) {
			return std::string();
		}
		int i;
		for (i = 0; i < (int)textsz - 7; i += 8) {
			des_crypt(SK, text + i, buffer + i);
		}
		uint8_t tail[8];
		if (!add_padding(tail, text + i, textsz - i, padding_mode)) {
			return std::string();
		}
		des_crypt(SK, tail, buffer + i);

		std::string buf((char*)buffer, chunksz);

		return buf;
	}

	//des 解码
	std::string desdecode(const std::string& in1, const std::string& in2) {
		uint32_t ESK[32];
		des_key(in1, ESK);
		uint32_t SK[32];
		int i;
		for (i = 0; i < 32; i += 2) {
			SK[i] = ESK[30 - i];
			SK[i + 1] = ESK[31 - i];
		}
		size_t textsz = in2.length();
		const uint8_t* text = (const uint8_t*)in2.c_str();
		if ((textsz & 7) || textsz == 0) {
			return std::string();
		}

		int padding_mode = 0;
		uint8_t tmp[256];
		uint8_t* buffer = tmp;
		if (textsz > 256) {
			return std::string();
		}
		for (i = 0; i < textsz; i += 8) {
			des_crypt(SK, text + i, buffer + i);
		}
		int padding = remove_padding(buffer + textsz - 1, padding_mode);
		if (padding <= 0 || padding > 8) {
			return std::string();
		}
		std::string buf((char*)buffer, textsz - padding);

		return buf;
	}

}

namespace BytesOpreation {
	TArray<uint8> Concat_BytesBytes(TArray<uint8> A, TArray<uint8> B)
	{
		TArray<uint8> ArrayResult;

		for (int i = 0; i < A.Num(); i++)
		{
			ArrayResult.Add(A[i]);
		}

		for (int i = 0; i < B.Num(); i++)
		{
			ArrayResult.Add(B[i]);
		}

		return ArrayResult;
	}

	TArray<uint8> Conv_IntToBytes(int32 InInt)
	{
		TArray<uint8> result;
		for (int i = 0; i < 4; i++)
		{
			result.Add(InInt >> i * 8);
		}
		return result;
	}

	TArray<uint8> Conv_StringToBytes(const FString& InStr)
	{
		FTCHARToUTF8 Convert(*InStr);
		int BytesLength = Convert.Length(); //length of the utf-8 string in bytes (when non-latin letters are used, it's longer than just the number of characters)
		uint8* messageBytes = static_cast<uint8*>(FMemory::Malloc(BytesLength));
		FMemory::Memcpy(messageBytes, (uint8*)TCHAR_TO_UTF8(InStr.GetCharArray().GetData()), BytesLength); //mcmpy is required, since TCHAR_TO_UTF8 returns an object with a very short lifetime

		TArray<uint8> result;
		for (int i = 0; i < BytesLength; i++)
		{
			result.Add(messageBytes[i]);
		}

		FMemory::Free(messageBytes);

		return result;
	}

	TArray<uint8> Conv_FloatToBytes(float InFloat)
	{
		TArray<uint8> result;

		unsigned char const* p = reinterpret_cast<unsigned char const*>(&InFloat);
		for (int i = 0; i != sizeof(float); i++)
		{
			result.Add((uint8)p[i]);
		}
		return result;
	}

	TArray<uint8> Conv_ByteToBytes(uint8 InByte)
	{
		TArray<uint8> result{ InByte };
		return result;
	}

	int32 Message_ReadInt(TArray<uint8>& Message)
	{
		if (Message.Num() < 4)
		{
			return -1;
		}

		int result;
		unsigned char byteArray[4];

		for (int i = 0; i < 4; i++)
		{
			byteArray[i] = Message[0];
			Message.RemoveAt(0);
		}

		FMemory::Memcpy(&result, byteArray, 4);

		return result;
	}

	uint8 Message_ReadByte(TArray<uint8>& Message)
	{
		if (Message.Num() < 1)
		{
			return 255;
		}

		uint8 result = Message[0];
		Message.RemoveAt(0);
		return result;
	}

	bool Message_ReadBytes(int32 NumBytes, TArray<uint8>& Message, TArray<uint8>& returnArray)
	{
		for (int i = 0; i < NumBytes; i++) {
			if (Message.Num() >= 1)
				returnArray.Add(Message_ReadByte(Message));
			else
				return false;
		}
		return true;
	}

	float Message_ReadFloat(TArray<uint8>& Message)
	{
		if (Message.Num() < 4)
		{
			return -1.f;
		}

		float result;
		unsigned char byteArray[4];

		for (int i = 0; i < 4; i++)
		{
			byteArray[i] = Message[0];
			Message.RemoveAt(0);
		}

		FMemory::Memcpy(&result, byteArray, 4);

		return result;
	}

	FString Message_ReadString(TArray<uint8>& Message, int32 BytesLength)
	{
		if (BytesLength <= 0 || Message.Num() < BytesLength)
		{
			return FString("");
		}

		TArray<uint8> StringAsArray;
		StringAsArray.Reserve(BytesLength);

		for (int i = 0; i < BytesLength; i++)
		{
			StringAsArray.Add(Message[0]);
			Message.RemoveAt(0);
		}

		std::string cstr(reinterpret_cast<const char*>(StringAsArray.GetData()), StringAsArray.Num());
		return FString(UTF8_TO_TCHAR(cstr.c_str()));
	}
}

void UYunntoGameInstance::Init()
{
	

	bIsSuccessLogin = false;

	OnServerConnected.BindUFunction(this, TEXT("ExecuteOnServerConnected"));
	OnServerDisconnected.BindUFunction(this, TEXT("ExecuteOnServerDisconnected"));
	OnServerMessageReceived.BindUFunction(this, TEXT("ExecuteOnServerMessageReceived"));

	TcpSocketActor = NewObject<ATcpSocket>();
	if (!TcpSocketActor)
		return;
	TcpSocketActor->AddToRoot();

	Super::Init();
}

void UYunntoGameInstance::Shutdown()
{
	TcpSocketActor->RemoveFromRoot();
	Super::Shutdown();
}

void UYunntoGameInstance::StartConnectSever()
{
	if (!TcpSocketActor)
	{
		return;
	}
	TcpSocketActor->Connect(ipAddress, port, OnServerDisconnected, OnServerConnected, OnServerMessageReceived);
}

void UYunntoGameInstance::ExecuteOnServerDisconnected()
{
	bIsSuccessLogin = false;
	NotifyServerDisconnected.Broadcast();
}

void UYunntoGameInstance::ExecuteOnServerConnected()
{
}

void UYunntoGameInstance::ExecuteOnServerMessageReceived(TArray<uint8>& Message)
{
	TArray<uint8> arr;
	if (!BytesOpreation::Message_ReadBytes(2, Message, arr))
	{
		return;
	}

	uint32_t len = (static_cast<uint32_t>(arr[0] << 8) + arr[1]);
	if (len > 1024 || len != Message.Num())
	{
		UE_LOG(LogTemp, Warning, TEXT("len:%u ; MessageNum:%d"), len, Message.Num());
	}
	//DataAnalysis(Message, len);

	while (static_cast<int32>(len) <= Message.Num())
	{
		DataAnalysis(Message, len);
		if (Message.Num() != 0)
		{
			arr.Empty();
			BytesOpreation::Message_ReadBytes(2, Message, arr);
			uint32_t TempLen = (static_cast<uint32_t>(arr[0] << 8) + arr[1]);
			UE_LOG(LogTemp, Warning, TEXT("len:%u ; MessageNum:%d"), TempLen, Message.Num());
			len = TempLen;
		}
	}
	
	

}

void UYunntoGameInstance::DataAnalysis(TArray<uint8>& Message, uint32_t len)
{
	FString StrMeeeage = BytesOpreation::Message_ReadString(Message, len);
	TSharedPtr<FJsonObject> MsgJsonObject;
	TSharedRef<TJsonReader<>> MsgJsonReader = TJsonReaderFactory<>::Create(StrMeeeage);
	if (FJsonSerializer::Deserialize(MsgJsonReader, MsgJsonObject))
	{
		uint32 code = 0;
		MsgJsonObject->TryGetNumberField(TEXT("code"), code);
		UE_LOG(LogTemp, Log, TEXT("收到code:%u"), code);
		switch (code)
		{
		case 4001:
		{
			FString tmp = MsgJsonObject->GetStringField(TEXT("challenge"));
			std::string cstrtmp = std::string(TCHAR_TO_UTF8(*tmp));
			cstrChallenge = crypt_unit::base64decode(cstrtmp);
			UE_LOG(LogTemp, Log, TEXT("challenge:%s"), UTF8_TO_TCHAR(cstrChallenge.c_str()));
			//std::string
			cstrClientkey = "22222222";
			//cstrClientkey = crypt_unit::randomkey();
			std::string cstrCkey = crypt_unit::dhexchange(cstrClientkey);
			UE_LOG(LogTemp, Log, TEXT("strCKey:%s"), UTF8_TO_TCHAR(cstrClientkey.c_str()));
			std::string cstrCkey64 = crypt_unit::base64encode(cstrCkey);
			FString StrCkey = FString(UTF8_TO_TCHAR(cstrCkey64.c_str()));
			UE_LOG(LogTemp, Log, TEXT("CK64:%s"), *StrCkey);
			TSharedPtr<FJsonObject> SendJsonObj = MakeShareable(new FJsonObject);
			SendJsonObj->SetNumberField(TEXT("code"), 2001);
			SendJsonObj->SetStringField(TEXT("ckey"), StrCkey);

			StrMeeeage.Empty();
			TSharedRef<TJsonWriter<TCHAR, TPrettyJsonPrintPolicy<TCHAR>>> Writer = TJsonWriterFactory<TCHAR, TPrettyJsonPrintPolicy<TCHAR>>::Create(&StrMeeeage);
			FJsonSerializer::Serialize(SendJsonObj.ToSharedRef(), Writer);

			TArray<uint8> Msg;
			Msg.Add(uint8(StrMeeeage.Len() / 256));
			Msg.Add(uint8(StrMeeeage.Len() % 256));
			Msg.Append(BytesOpreation::Conv_StringToBytes(StrMeeeage));
			TcpSocketActor->SendData(Msg);

		}
		break;
		case 4002:
		{
			FString StrSkey = MsgJsonObject->GetStringField(TEXT("skey"));
			if (StrSkey.IsEmpty())
			{
				break;
			}
			cstrSkey64 = crypt_unit::base64decode(std::string(TCHAR_TO_UTF8(*StrSkey)));

			std::string tLog1 = crypt_unit::hexencode(cstrSkey64);
			UE_LOG(LogTemp, Log, TEXT("strSKey:%s"), UTF8_TO_TCHAR(tLog1.c_str()));

			std::string cstrSecret;
			cstrSecret = crypt_unit::dhsecret(cstrSkey64, cstrClientkey);

			std::string tLog2 = crypt_unit::hexencode(cstrSecret);
			UE_LOG(LogTemp, Log, TEXT("cstrSecret:%s"), UTF8_TO_TCHAR(tLog2.c_str()));

			std::string cstrChmac = crypt_unit::hmac64(cstrChallenge, cstrSecret);

			std::string tLog3 = crypt_unit::hexencode(cstrChmac);
			UE_LOG(LogTemp, Log, TEXT("cstrChmac:%s"), UTF8_TO_TCHAR(tLog3.c_str()));

			TSharedPtr<FJsonObject> SendJsonObj = MakeShareable(new FJsonObject);
			SendJsonObj->SetNumberField(TEXT("code"), 2002);
			SendJsonObj->SetStringField(TEXT("chmac"), FString(UTF8_TO_TCHAR(crypt_unit::base64encode(cstrChmac).c_str())));

			StrMeeeage.Empty();
			TSharedRef<TJsonWriter<TCHAR, TPrettyJsonPrintPolicy<TCHAR>>> Writer = TJsonWriterFactory<TCHAR, TPrettyJsonPrintPolicy<TCHAR>>::Create(&StrMeeeage);
			FJsonSerializer::Serialize(SendJsonObj.ToSharedRef(), Writer);

			UE_LOG(LogTemp, Log, TEXT("4002:%s"), *StrMeeeage);
			TArray<uint8> Msg;
			Msg.Add(StrMeeeage.Len() / 256);
			Msg.Add(StrMeeeage.Len() % 256);
			Msg.Append(BytesOpreation::Conv_StringToBytes(StrMeeeage));
			TcpSocketActor->SendData(Msg);

			FString StrMacID = UBPFunctionLibBPLibrary::GetYunnToUniqueID();
			std::string cstrMacID = std::string(TCHAR_TO_UTF8(*StrMacID));
			std::string cstrMacID64 = crypt_unit::base64encode(cstrMacID);
			std::string cstrPjc = std::string(TCHAR_TO_UTF8(*ProjectName));
			std::string cstrPjc64 = crypt_unit::base64encode(cstrPjc);

			std::string cstrToken;
			cstrToken += cstrMacID64;
			cstrToken += ":";
			cstrToken += cstrPjc64;

			std::string etoken;
			etoken = crypt_unit::desencode(cstrSecret, cstrToken);
			std::string etoken64 = crypt_unit::base64encode(etoken);
			TSharedPtr<FJsonObject> SendJsonObj2 = MakeShareable(new FJsonObject);
			SendJsonObj2->SetNumberField(TEXT("code"), 2003);
			SendJsonObj2->SetStringField(TEXT("etoken"), FString(UTF8_TO_TCHAR(etoken64.c_str())));

			StrMeeeage.Empty();
			TSharedRef<TJsonWriter<TCHAR, TPrettyJsonPrintPolicy<TCHAR>>> Writer2 = TJsonWriterFactory<TCHAR, TPrettyJsonPrintPolicy<TCHAR>>::Create(&StrMeeeage);
			FJsonSerializer::Serialize(SendJsonObj2.ToSharedRef(), Writer2);

			UE_LOG(LogTemp, Log, TEXT("4002:%s"), *StrMeeeage);

			Msg.Empty();
			Msg.Add(StrMeeeage.Len() / 256);
			Msg.Add(StrMeeeage.Len() % 256);
			Msg.Append(BytesOpreation::Conv_StringToBytes(StrMeeeage));
			TcpSocketActor->SendData(Msg);
		}
		break;
		case 4003:
		{
			uint32 succ = 0;
			MsgJsonObject->TryGetNumberField(TEXT("succ"), succ);
			if (succ)
			{
				//连接服务器成功,发送心跳包;
				bIsSuccessLogin = true;
				/*
				GetTimerManager().SetTimer(TH_SendHeartbeatPak, [this]() {

					if (bIsSuccessLogin)
					{
						TSharedPtr<FJsonObject> SendBeatJsonObj = MakeShareable(new FJsonObject);
						SendBeatJsonObj->SetNumberField(TEXT("code"), 2000);

						FString beatString;
						TSharedRef<TJsonWriter<TCHAR, TPrettyJsonPrintPolicy<TCHAR>>> Writer = TJsonWriterFactory<TCHAR, TPrettyJsonPrintPolicy<TCHAR>>::Create(&beatString);
						FJsonSerializer::Serialize(SendBeatJsonObj.ToSharedRef(), Writer);

						TArray<uint8> Msg;
						Msg.Add(beatString.Len() / 256);
						Msg.Add(beatString.Len() % 256);
						Msg.Append(BytesOpreation::Conv_StringToBytes(beatString));
						TcpSocketActor->SendData(Msg);
					}
				}, 10.0f, true);
				*/
				NotifyLoginSuccess.Broadcast();
			}
			else
			{
				//连接服务器失败；
				UE_LOG(LogTemp, Log, TEXT("connection failed"));
			}
		}
		break;
		case 4101:
		{
			//不在白名单，非白名单的allow为false
			UE_LOG(LogTemp, Log, TEXT("该设备不在白名单"));
			NotifyNotInWhiteList.Broadcast();
		}
		break;
		case 4102:
		{
			//项目到期
			UE_LOG(LogTemp, Log, TEXT("项目到期"));
			NotifyProjectDue.Broadcast();
		}
		break;
		case 4103:
		{
			//项目暂停计时
			UE_LOG(LogTemp, Log, TEXT("项目暂停计时"));
			NotifyProjectPause.Broadcast();
		}
		break;
		case 4104:
		{
			int32 rehours = MsgJsonObject->GetIntegerField(TEXT("expire_hour"));
			//剩余时间数
			UE_LOG(LogTemp, Log, TEXT("剩余使用时间：%d"), rehours);
			NotifyExpireHour.Broadcast(rehours);
		}
		break;
		case 4105:
		{
			int32 rehours = MsgJsonObject->GetIntegerField(TEXT("expire_hour"));
			//剩余时间数
			UE_LOG(LogTemp, Log, TEXT("剩余使用时间修改：%d"), rehours);
			NotifyExpireHourUpdate.Broadcast(rehours);
		}
		break;
		case 4106:
		{
			int32 logo = MsgJsonObject->GetIntegerField(TEXT("logo"));
			UE_LOG(LogTemp, Log, TEXT("显示logo：%d"), logo );
			NotifyLogoState.Broadcast(logo);
		}
		break;
		default:
			UE_LOG(LogTemp, Log, TEXT("异常code"));
			break;
		}
	}
}

// 显示编辑器通知消息（右下角）
void UYunntoGameInstance::ShowEditorNotification(const FString& Message)
{

	

	
#if WITH_EDITOR
	// 检查是否已有通知存在
	if (CurrentNotificationItem.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("通知已存在，不创建新通知"));
		return;
	}

	FNotificationInfo NotificationInfo(FText::FromString(Message));

	// 设置通知属性 - 不自动消失
	NotificationInfo.bFireAndForget = false;        // 不自动消失
	NotificationInfo.FadeOutDuration = 0.5f;        // 淡出时间
	NotificationInfo.ExpireDuration = 0.0f;         // 不设置过期时间，永不自动消失
	NotificationInfo.bUseThrobber = false;          // 不使用加载动画
	NotificationInfo.bUseSuccessFailIcons = true;   // 使用成功/失败图标
    

	// 添加按钮
	NotificationInfo.ButtonDetails.Add(FNotificationButtonInfo(
		FText::FromString(TEXT("刷新相机参数")),
		FText::FromString(TEXT("点击刷新相机参数")),
		FSimpleDelegate::CreateLambda([this]()
		{
			// 关闭当前通知
			if (CurrentNotificationItem.IsValid())
			{
				CurrentNotificationItem->SetCompletionState(SNotificationItem::CS_Success);
				CurrentNotificationItem->ExpireAndFadeout();
				CurrentNotificationItem.Reset(); // 清空引用
			}

			// 调用蓝图可响应的函数
			//OnRefreshAllCamera.Broadcast();

			
			
		})
	));

	// 创建通知
	CurrentNotificationItem = FSlateNotificationManager::Get().AddNotification(NotificationInfo);

	if (CurrentNotificationItem.IsValid())
	{
		CurrentNotificationItem->SetCompletionState(SNotificationItem::CS_Pending);
		UE_LOG(LogTemp, Warning, TEXT("编辑器通知已显示: %s"), *Message);
	}
#endif
}




