// Fill out your copyright notice in the Description page of Project Settings.


#include "YunnToPOI.h"
#include "CesiumGeoreference.h"
#include "Runtime/Engine/Classes/Engine/Texture2DDynamic.h"
#include "Editor.h"
#include "Runtime/Engine/Public/TextureResource.h"
#include "Runtime/Online/HTTP/Public/Interfaces/IHttpRequest.h"
#include "Runtime/Online/HTTP/Public/HttpModule.h"
#include "Runtime/Online/HTTP/Public/Interfaces/IHttpResponse.h"
#include "Runtime/ImageWrapper/Public/IImageWrapperModule.h"



// Sets default values
AYunnToPOI::AYunnToPOI()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
	UnrealHeight = 0.0f;
}

// Called when the game starts or when spawned
void AYunnToPOI::BeginPlay()
{
	Super::BeginPlay();
	
	
}

// Called every frame
void AYunnToPOI::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

void AYunnToPOI::ConvertJsonToPOIAttributes(TMap<FString, FPOIAttributes>& OutAmenitiesMap,FString POIJson)
{

	//����json
	TArray<TSharedPtr<FJsonValue>> JsonArray;
	TSharedRef<TJsonReader<>> JsonReader = TJsonReaderFactory<>::Create(POIJson);
	// �����л� JSON ����
	if (!FJsonSerializer::Deserialize(JsonReader, JsonArray))
	{
		UE_LOG(LogTemp, Error, TEXT("BPFunction*** Failed to deserialize JSON ***"));
		
	}

	//��ʼ��ӵ���ֵ���� ����JsonArray��ÿһ��Ԫ��
	for (const TSharedPtr<FJsonValue>& JsonValue : JsonArray)
	{
		// �� JSON ����ת��Ϊ FSurroundingAmenities
		TSharedPtr<FJsonObject> JsonObject = JsonValue->AsObject();
		if (!JsonObject.IsValid())
		{
			//JsonObjectת��ʧ��
			UE_LOG(LogTemp, Warning, TEXT("BPFunction*** Invalid JSON object ***"));

			// ������ǰѭ��������������һ�� JsonValue
			continue;
		}
		FPOIAttributes POIAttribute;
		//����json
	
	   

		////POI id
		POIAttribute.PA_Id = JsonObject->GetNumberField(TEXT("id"));

		//POI����
		POIAttribute.PA_Name= JsonObject->GetStringField(TEXT("name"));

		////POIλ��
		/*FString LocationStr = JsonObject->GetStringField(TEXT("location"));
		ParsedLocation.InitFromString(LocationStr);*/
		FVector ParsedLocation = FVector(JsonObject->GetNumberField(TEXT("locationX")), JsonObject->GetNumberField(TEXT("locationY")),JsonObject->GetNumberField(TEXT("locationZ")));
	    //��γ��ת����unreal����
		POIAttribute.PA_Location = CesiumGeoreference->TransformLongitudeLatitudeHeightPositionToUnreal(ParsedLocation);
		
	
		

		// POI Tag
		if (JsonObject->HasField(TEXT("tags")))
		{
			FString TagsStr = JsonObject->GetStringField(TEXT("tags"));
			if (!TagsStr.IsEmpty())
			{
				TagsStr.ParseIntoArray(POIAttribute.PA_Tags, TEXT("|"));
			}
		}



		TSharedPtr<FJsonObject> StyleObject = JsonObject->GetObjectField("style");
		if (StyleObject.IsValid())
		{
			//�Ƿ�ʹ��widget
			StyleObject->TryGetBoolField(TEXT("usedWidget"), POIAttribute.PA_UsedWidget);

			//POI ����  
			StyleObject->TryGetNumberField(TEXT("widgetStyle"), POIAttribute.PA_WidgetStyle);
		/*	int32 SwitchWidgetStyle;
			StyleObject->TryGetNumberField(TEXT("widgetStyle"), SwitchWidgetStyle);*/

			//�Ƿ���ʾ��ǩСͼ��
			StyleObject->TryGetBoolField(TEXT("showHeaderImage"), POIAttribute.PA_ShowHeaderImage);

			//POI����ͼƬ
			StyleObject->TryGetStringField(TEXT("headerImage"), POIAttribute.PA_HeaderImage);

			//����ͼǰ�� 
			StyleObject->TryGetStringField(TEXT("backgroundImage1"), POIAttribute.PA_BackgroundImage1);

			//����ͼ�ж�
			StyleObject->TryGetStringField(TEXT("backgroundImage2"), POIAttribute.PA_BackgroundImage2);

			//����ͼ���   
			StyleObject->TryGetStringField(TEXT("backgroundImage3"), POIAttribute.PA_BackgroundImage3);

			//������ʽ 
			StyleObject->TryGetNumberField(TEXT("fontStyle"), POIAttribute.PA_FontStyle);

			//������ɫ
			FString ColorString;
			if (StyleObject->TryGetStringField(TEXT("fontColor"), ColorString))
			{
				ColorString = ColorString.Replace(TEXT("rgba("), TEXT("")).Replace(TEXT(")"), TEXT(""));
				TArray<FString> ColorComponents;
				ColorString.ParseIntoArray(ColorComponents, TEXT(", "), true);
				if (ColorComponents.Num() == 4)
				{
					float Red = FCString::Atof(*ColorComponents[0]) / 255.0f;
					float Green = FCString::Atof(*ColorComponents[1]) / 255.0f;
					float Blue = FCString::Atof(*ColorComponents[2]) / 255.0f;
					float Alpha = FCString::Atof(*ColorComponents[3]);
					POIAttribute.PA_FontColor = FLinearColor(Red, Green, Blue, Alpha);
				}
			}

			//���ַŴ���С
			StyleObject->TryGetNumberField(TEXT("fontEnlargement"), POIAttribute.PA_FontEnlargement);

			//�Ƿ���Ե��
			StyleObject->TryGetBoolField(TEXT("canClick"), POIAttribute.PA_CanClick);

			//���ͼƬ  
			StyleObject->TryGetStringField(TEXT("clickImage"), POIAttribute.PA_ClickImage);

			//POI��С����
			StyleObject->TryGetNumberField(TEXT("widgetBrushSizeNum"), POIAttribute.PA_WidgetBrushSizeNum);

			//��ǩСͼ���С���� 
			StyleObject->TryGetNumberField(TEXT("headerImageSizeNum"), POIAttribute.PA_HeaderImageSizeNum);

			//�����ǩ����
			StyleObject->TryGetNumberField(TEXT("clickImageSizeNum"), POIAttribute.PA_ClickImageSizeNum);

			//--------------------��ǩ�ߺͶ�����ʽ(ͨ��)------------------------//
			//��ǩ��ͼƬ
			StyleObject->TryGetStringField(TEXT("lineImage"), POIAttribute.PA_LineImage);
			//��ǩ�߳���
			StyleObject->TryGetNumberField(TEXT("lineLength"), POIAttribute.PA_LineLength);
			//��ǩ�߿��
			StyleObject->TryGetNumberField(TEXT("lineWidth"), POIAttribute.PA_LineWidth);
			//��ǩ���ϵĵ�ͼƬ
			StyleObject->TryGetStringField(TEXT("linePointImage"), POIAttribute.PA_LinePointImage);
			//��ǩ���ϵĵ�ͼƬ��С
			StyleObject->TryGetNumberField(TEXT("linePointImageSizeNum"), POIAttribute.PA_LinePointImageSizeNum);
			//POI ��������(������ʽ)
			StyleObject->TryGetNumberField(TEXT("openType"), POIAttribute.PA_OpenType);
			//����ʱ��
			StyleObject->TryGetNumberField(TEXT("animTime"), POIAttribute.PA_AnimTime);


		
		if(TSharedPtr<FJsonObject> HorizontalStyle = StyleObject->GetObjectField("horizontal");
			HorizontalStyle.IsValid() && HorizontalStyle->Values.Num()>0)
			{
			
				//�����ǩ����
				HorizontalStyle->TryGetBoolField(TEXT("mirror"), POIAttribute.PA_HorizontalMirror);
				//�����ǩ����ת
				HorizontalStyle->TryGetNumberField(TEXT("lineRotation"), POIAttribute.PA_HorizontalLineRotation);
				//��ǩ��λ��
				HorizontalStyle->TryGetNumberField(TEXT("linePosition"), POIAttribute.PA_HorizontalLinePosition);
				//ǰ������
				HorizontalStyle->TryGetNumberField(TEXT("lineLeftBlank"), POIAttribute.PA_HorizontalLineLeftBlank);
				//β������
				HorizontalStyle->TryGetNumberField(TEXT("lineRightBlank"), POIAttribute.PA_HorizontalLineRightBlank);
				//���־���Сͼ�����
				HorizontalStyle->TryGetNumberField(TEXT("headerImageTextDistance"), POIAttribute.PA_HeaderImageTextDistance);
				//��ǩ�����ǩ�߾���
				HorizontalStyle->TryGetNumberField(TEXT("lineWidgetDistance"), POIAttribute.PA_LineWidgetDistance);
				//�������¼�����
				HorizontalStyle->TryGetNumberField(TEXT("textBottomDistance"), POIAttribute.PA_TextBottomDistance);

			}
	
			if (TSharedPtr<FJsonObject> VerticalStyle = StyleObject->GetObjectField("vertical");
				VerticalStyle.IsValid() && VerticalStyle->Values.Num() > 0)
			{
				//�����ǩ�߾���
				VerticalStyle->TryGetNumberField(TEXT("lineWidgetDistance"), POIAttribute.PA_VerticalLineWidgetDistance);
				//���������ǩ����
				VerticalStyle->TryGetBoolField(TEXT("mirror"), POIAttribute.PA_VerticalMirror);
				//�����ǩ�������� 
				VerticalStyle->TryGetNumberField(TEXT("lineTopBlank"), POIAttribute.PA_VerticalLineTopBlank);
				//�����ǩ�ײ�����
				VerticalStyle->TryGetNumberField(TEXT("lineBottomBlank"), POIAttribute.PA_VerticalLineBottomBlank);
				//�����ǩ�߾��붥������
				VerticalStyle->TryGetNumberField(TEXT("lineDistanceTop"), POIAttribute.PA_VerticalLineDistanceTop);
			}

		}


		//���key
		FString ParsedName = JsonObject->GetStringField(TEXT("id"));
		OutAmenitiesMap.Add(ParsedName, POIAttribute);
	}
	
	UE_LOG(LogTemp, Display, TEXT("Successfully imported %d amenities"), OutAmenitiesMap.Num());
	
	  
}





void AYunnToPOI::LoadTextureFromURL(const FString& ImagePathOrURL)
{

	/*// ���� Http ����
	TSharedRef<IHttpRequest> HttpRequest = FHttpModule::Get().CreateRequest();

	// �첽����ͼƬ����
	HttpRequest->OnProcessRequestComplete().BindLambda([this, ImageUrl](FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful)
		{
			if (bWasSuccessful && Response.IsValid() && Response->GetResponseCode() == EHttpResponseCodes::Ok)
			{
				const TArray<uint8>& ImageData = Response->GetContent();

				// �����ص�ͼ������ת��Ϊ UTexture2D
				UTexture2D* Texture = LoadTextureFromBytes(ImageData);

				// ����ɹ����������򴥷��ص�����������
				if (Texture)
				{
					OnTextureLoaded.Broadcast(Texture, ImageUrl);
				}
				else
				{
					UE_LOG(LogTemp, Error, TEXT("Failed to load texture from URL: %s"), *ImageUrl);
				}
			}
			else
			{
				UE_LOG(LogTemp, Error, TEXT("Failed to download image from URL: %s"), *ImageUrl);
			}
		});

	HttpRequest->SetURL(ImageUrl);  // ��������� URL
	HttpRequest->SetVerb("GET");    // ʹ�� GET ����
	HttpRequest->ProcessRequest();  // ��������*/

	// 判断是网络 URL 还是本地文件路径
	if (ImagePathOrURL.StartsWith(TEXT("http://")) || ImagePathOrURL.StartsWith(TEXT("https://")))
	{
		// --- 处理网络 URL 
		TSharedRef<IHttpRequest> HttpRequest = FHttpModule::Get().CreateRequest();

		HttpRequest->OnProcessRequestComplete().BindLambda([this, ImagePathOrURL](FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful)
			{
				if (bWasSuccessful && Response.IsValid() && Response->GetResponseCode() == EHttpResponseCodes::Ok)
				{
					const TArray<uint8>& ImageData = Response->GetContent();
					UTexture2D* Texture = LoadTextureFromBytes(ImageData);

					if (Texture)
					{
						OnTextureLoaded.Broadcast(Texture, ImagePathOrURL);
					}
					else
					{
						UE_LOG(LogTemp, Error, TEXT("Failed to create texture from URL data: %s"), *ImagePathOrURL);
					}
				}
				else
				{
					UE_LOG(LogTemp, Error, TEXT("Failed to download image from URL: %s"), *ImagePathOrURL);
				}
			});

		HttpRequest->SetURL(ImagePathOrURL);
		HttpRequest->SetVerb("GET");
		HttpRequest->ProcessRequest();
	}
	else
	{
		// --- 处理本地文件路径 ---
		// 检查文件是否存在
		if (!FPaths::FileExists(ImagePathOrURL))
		{
			UE_LOG(LogTemp, Error, TEXT("Local image file not found at path: %s"), *ImagePathOrURL);
			return;
		}

		TArray<uint8> ImageData;
		// 使用 FFileHelper 同步加载本地文件到字节数组
		if (FFileHelper::LoadFileToArray(ImageData, *ImagePathOrURL))
		{
			// 从字节数组创建纹理
			UTexture2D* Texture = LoadTextureFromBytes(ImageData);

			if (Texture)
			{
				FTimerDelegate TimerDelegate;
				TimerDelegate.BindLambda([this, Texture, ImagePathOrURL]()
					{
						if (IsValid(this) && IsValid(Texture))
						{
							//从本地文件路径中提取基础文件名再进行广播
							FString BaseFilename = FPaths::GetBaseFilename(ImagePathOrURL);
							OnTextureLoaded.Broadcast(Texture, BaseFilename);
						}
					});

				if (UWorld* World = GetWorld())
				{
					// 【已修正】先声明一个 FTimerHandle 变量
					FTimerHandle TimerHandle;
					World->GetTimerManager().SetTimer(TimerHandle, TimerDelegate, 0.001f, false);
				}
			}
			else
			{
				UE_LOG(LogTemp, Error, TEXT("Failed to create texture from local file: %s"), *ImagePathOrURL);
			}
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to load local file to array: %s"), *ImagePathOrURL);
		}
	}


}

UTexture2D* AYunnToPOI::LoadTextureFromBytes(const TArray<uint8>& ImageData)
{
	IImageWrapperModule& ImageWrapperModule = FModuleManager::LoadModuleChecked<IImageWrapperModule>("ImageWrapper");
	TSharedPtr<IImageWrapper> ImageWrapper = ImageWrapperModule.CreateImageWrapper(EImageFormat::PNG);

	if (ImageWrapper.IsValid() && ImageWrapper->SetCompressed(ImageData.GetData(), ImageData.Num()))
	{
		TArray<uint8> UncompressedRGBA;
		if (ImageWrapper->GetRaw(ERGBFormat::RGBA, 8, UncompressedRGBA))
		{
			UTexture2D* Texture = UTexture2D::CreateTransient(
				ImageWrapper->GetWidth(),
				ImageWrapper->GetHeight(),
				PF_R8G8B8A8
			);

			if (Texture != nullptr)
			{
				void* TextureData = Texture->GetPlatformData()->Mips[0].BulkData.Lock(LOCK_READ_WRITE);
				FMemory::Memcpy(TextureData, UncompressedRGBA.GetData(), UncompressedRGBA.Num());
				Texture->GetPlatformData()->Mips[0].BulkData.Unlock();

                  #ifdef _WIN32
                  #undef UpdateResource
                  #endif

				Texture->UpdateResource();
				return Texture;
			}
		}
	}
	return nullptr;
}



bool AYunnToPOI::SetLocalPOILocation(const FString& JsonFilePath, int32 ItemId, const FString& NewLocationX, const FString& NewLocationY, const FString& NewLocationZ)
{

	
	// 读取JSON文件
	FString JsonString;
	if (!FFileHelper::LoadFileToString(JsonString, *JsonFilePath))
	{
		UE_LOG(LogTemp, Error, TEXT("无法读取JSON文件: %s"), *JsonFilePath);
		return false;
	}

	TArray<TSharedPtr<FJsonValue>> JsonArray;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

	if (!FJsonSerializer::Deserialize(Reader, JsonArray))
	{
		UE_LOG(LogTemp, Error, TEXT("JSON文件解析失败: %s"), *JsonFilePath);
		return false;
	}

	// 查找并修改目标位置 - 使用转换后的浮点数
	bool bFound = false;
	if (!ModifyLocationInJsonArray(JsonArray, ItemId, NewLocationX, NewLocationY, NewLocationZ, bFound))
	{
		UE_LOG(LogTemp, Error, TEXT("修改过程中发生错误"));
		return false;
	}

	if (!bFound)
	{
		UE_LOG(LogTemp, Warning, TEXT("未找到ID为 %d 的数据项"), ItemId);
		return false;
	}

	// 序列化并保存回原文件
	FString OutputString;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
	FJsonSerializer::Serialize(JsonArray, Writer);

	if (!FFileHelper::SaveStringToFile(OutputString, *JsonFilePath))
	{
		UE_LOG(LogTemp, Error, TEXT("无法保存JSON文件: %s"), *JsonFilePath);
		return false;
	}

	UE_LOG(LogTemp, Log, TEXT("成功修改ID %d 的位置坐标为 (%s, %s, %s) 并保存到: %s"),
		ItemId, *NewLocationX, *NewLocationY, *NewLocationZ, *JsonFilePath);
	return true;
}

bool AYunnToPOI::ModifyLocationInJsonArray(TArray<TSharedPtr<FJsonValue>>& JsonArray, int32 TargetId, const FString& NewLocationX, const FString& NewLocationY, const FString& NewLocationZ, bool& bFound)
{
	bFound = false;
	for (TSharedPtr<FJsonValue>& Item : JsonArray)
	{
		if (Item.IsValid() && Item->Type == EJson::Object)
		{
			TSharedPtr<FJsonObject> Obj = Item->AsObject();
			int32 Id;
			if (Obj->TryGetNumberField(TEXT("id"), Id) && Id == TargetId)
			{
				// 直接存字符串，保证精度
				Obj->SetStringField(TEXT("locationX"), NewLocationX);
				Obj->SetStringField(TEXT("locationY"), NewLocationY);
				Obj->SetStringField(TEXT("locationZ"), NewLocationZ);

				FString LocationString = FString::Printf(TEXT("X=%s Y=%s Z=%s"),
					*NewLocationX, *NewLocationY, *NewLocationZ);
				Obj->SetStringField(TEXT("location"), LocationString);

				bFound = true;
				return true; // 找到后直接返回
			}
		}
	}
	return true; // 没找到也返回true，因为bFound已经标记
}

void AYunnToPOI::RefreshCameraParameters()
{
	OnRefreshCameraEvent.Broadcast();
	
}

void AYunnToPOI::CallBlueprintFunction(const FString& FunctionName)
{
	FEditorScriptExecutionGuard ScriptGuard;

	UClass* MyClass = GetClass();
	if (!MyClass)
	{
		UE_LOG(LogTemp, Error, TEXT("无法获取类信息"));
		return;
	}
    
	UFunction* Function = MyClass->FindFunctionByName(*FunctionName);
	if (!Function)
	{
		UE_LOG(LogTemp, Error, TEXT("找不到函数: %s"), *FunctionName);
		return;
	}
    
	// 检查这个函数是否可以在编辑器中执行，这是一个很好的安全检查
	if (!Function->HasAnyFunctionFlags(FUNC_EditorOnly))
	{
		UE_LOG(LogTemp, Warning, TEXT("警告: 函数 '%s' 没有被标记为 'CallInEditor'，在编辑器中调用可能不安全。"), *FunctionName);
	}
    
	// 调用函数
	ProcessEvent(Function, nullptr);
	UE_LOG(LogTemp, Warning, TEXT("成功调用蓝图函数: %s"), *FunctionName);
	
}
