// Fill out your copyright notice in the Description page of Project Settings.


#include "YunnToPolygon.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonReader.h"
#include "Engine/Engine.h"
#include "Misc/FileHelper.h"
#include "HAL/PlatformFilemanager.h"

// Sets default values
AYunnToPolygon::AYunnToPolygon()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void AYunnToPolygon::BeginPlay()
{
	Super::BeginPlay();

}

// Called every frame
void AYunnToPolygon::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

// 辅助函数：解析坐标数组
TArray<FVector2D> AYunnToPolygon::ParseCoordinateArray(const TArray<TSharedPtr<FJsonValue>>& CoordArray)
{
	TArray<FVector2D> Coordinates;

	for (const auto& CoordValue : CoordArray)
	{
		const TArray<TSharedPtr<FJsonValue>>* CoordPair;
		if (CoordValue->TryGetArray(CoordPair) && CoordPair->Num() >= 2)
		{
			double X, Y;
			if ((*CoordPair)[0]->TryGetNumber(X) && (*CoordPair)[1]->TryGetNumber(Y))
			{
				Coordinates.Add(FVector2D(X, Y));
			}
		}
	}

	return Coordinates;
}


// 辅助函数：解析单个Feature对象并提取坐标
FSimpleGeoJsonItem AYunnToPolygon::ParseSingleFeatureToSimple(
	const TSharedPtr<FJsonObject>& FeatureObject,
	const FString& PropertyKey1,
	const FString& PropertyKey2,
	const FString& PropertyKey3)
{
	FSimpleGeoJsonItem SimpleItem;

	if (!FeatureObject.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Invalid Feature object"));
		return SimpleItem;
	}

	// 检查是否为Feature类型
	FString Type;
	if (!FeatureObject->TryGetStringField(TEXT("type"), Type) || Type != TEXT("Feature"))
	{
		UE_LOG(LogTemp, Warning, TEXT("Object is not a Feature type, found: %s"), *Type);
		return SimpleItem;
	}

	// 解析properties
	const TSharedPtr<FJsonObject>* PropertiesObject;
	if (FeatureObject->TryGetObjectField(TEXT("properties"), PropertiesObject) && PropertiesObject->IsValid())
	{
		// 提取自定义属性
		(*PropertiesObject)->TryGetStringField(PropertyKey1, SimpleItem.Property1);
		(*PropertiesObject)->TryGetStringField(PropertyKey2, SimpleItem.Property2);
		(*PropertiesObject)->TryGetStringField(PropertyKey3, SimpleItem.Property3);
	}

	// 解析geometry
	const TSharedPtr<FJsonObject>* GeometryObject;
	if (!FeatureObject->TryGetObjectField(TEXT("geometry"), GeometryObject) || !GeometryObject->IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("No geometry field found in Feature"));
		return SimpleItem;
	}

	// 检查geometry类型是否为MultiPolygon
	FString GeometryType;
	if (!(*GeometryObject)->TryGetStringField(TEXT("type"), GeometryType) || GeometryType != TEXT("MultiPolygon"))
	{
		UE_LOG(LogTemp, Warning, TEXT("Geometry type is not MultiPolygon, found: %s"), *GeometryType);
		return SimpleItem;
	}

	// 解析coordinates - 不再扁平化，而是返回基础数据供调用者处理
	const TArray<TSharedPtr<FJsonValue>>* CoordinatesArray;
	if ((*GeometryObject)->TryGetArrayField(TEXT("coordinates"), CoordinatesArray))
	{
		// 只解析第一个多边形的第一个环作为默认返回
		// 实际的多边形拆分将在调用函数中处理
		if (CoordinatesArray->Num() > 0)
		{
			const auto& FirstPolygonValue = (*CoordinatesArray)[0];
			const TArray<TSharedPtr<FJsonValue>>* PolygonRings;
			if (FirstPolygonValue->TryGetArray(PolygonRings) && PolygonRings->Num() > 0)
			{
				const auto& FirstRingValue = (*PolygonRings)[0];
				const TArray<TSharedPtr<FJsonValue>>* RingCoords;
				if (FirstRingValue->TryGetArray(RingCoords))
				{
					SimpleItem.Coordinates = ParseCoordinateArray(*RingCoords);
				}
			}
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("No coordinates field found in geometry"));
	}

	return SimpleItem;
}

// 辅助函数：将单个Feature拆分为多个多边形项
TArray<FSimpleGeoJsonItem> AYunnToPolygon::SplitFeatureIntoPolygons(
	const TSharedPtr<FJsonObject>& FeatureObject,
	const FString& PropertyKey1,
	const FString& PropertyKey2,
	const FString& PropertyKey3)
{
	TArray<FSimpleGeoJsonItem> PolygonItems;

	if (!FeatureObject.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Invalid Feature object"));
		return PolygonItems;
	}

	// 检查是否为Feature类型
	FString Type;
	if (!FeatureObject->TryGetStringField(TEXT("type"), Type) || Type != TEXT("Feature"))
	{
		UE_LOG(LogTemp, Warning, TEXT("Object is not a Feature type, found: %s"), *Type);
		return PolygonItems;
	}

	// 解析properties
	FString BaseProperty1, BaseProperty2, BaseProperty3;
	const TSharedPtr<FJsonObject>* PropertiesObject;
	if (FeatureObject->TryGetObjectField(TEXT("properties"), PropertiesObject) && PropertiesObject->IsValid())
	{
		// 提取自定义属性
		(*PropertiesObject)->TryGetStringField(PropertyKey1, BaseProperty1);
		(*PropertiesObject)->TryGetStringField(PropertyKey2, BaseProperty2);
		(*PropertiesObject)->TryGetStringField(PropertyKey3, BaseProperty3);
	}

	// 解析geometry
	const TSharedPtr<FJsonObject>* GeometryObject;
	if (!FeatureObject->TryGetObjectField(TEXT("geometry"), GeometryObject) || !GeometryObject->IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("No geometry field found in Feature"));
		return PolygonItems;
	}

	// 检查geometry类型是否为MultiPolygon
	FString GeometryType;
	if (!(*GeometryObject)->TryGetStringField(TEXT("type"), GeometryType) || GeometryType != TEXT("MultiPolygon"))
	{
		UE_LOG(LogTemp, Warning, TEXT("Geometry type is not MultiPolygon, found: %s"), *GeometryType);
		return PolygonItems;
	}

	// 解析coordinates并拆分每个多边形
	const TArray<TSharedPtr<FJsonValue>>* CoordinatesArray;
	if ((*GeometryObject)->TryGetArrayField(TEXT("coordinates"), CoordinatesArray))
	{
		UE_LOG(LogTemp, Log, TEXT("发现 %d 个多边形，开始拆分"), CoordinatesArray->Num());

		// 遍历每个多边形
		for (int32 PolygonIndex = 0; PolygonIndex < CoordinatesArray->Num(); PolygonIndex++)
		{
			const auto& PolygonValue = (*CoordinatesArray)[PolygonIndex];
			const TArray<TSharedPtr<FJsonValue>>* PolygonRings;

			if (PolygonValue->TryGetArray(PolygonRings))
			{
				FSimpleGeoJsonItem PolygonItem;

				// 设置属性值，Property1后面添加多边形索引
				PolygonItem.Property1 = FString::Printf(TEXT("%s*%d"), *BaseProperty1, PolygonIndex);
				PolygonItem.Property2 = BaseProperty2;  // Property2保持不变
				PolygonItem.Property3 = BaseProperty3;  // Property3保持不变

				// 解析当前多边形的所有环的坐标
				for (const auto& RingValue : *PolygonRings)
				{
					const TArray<TSharedPtr<FJsonValue>>* RingCoords;
					if (RingValue->TryGetArray(RingCoords))
					{
						// 解析并添加当前环的所有坐标点
						TArray<FVector2D> RingPoints = ParseCoordinateArray(*RingCoords);
						for (const FVector2D& Point : RingPoints)
						{
							PolygonItem.Coordinates.Add(Point);
						}
					}
				}

				// 只有当多边形有坐标点时才添加
				if (PolygonItem.Coordinates.Num() > 0)
				{
					PolygonItems.Add(PolygonItem);
					UE_LOG(LogTemp, Log, TEXT("多边形 %d 拆分完成: %s, 坐标点数量: %d"),
						PolygonIndex, *PolygonItem.Property1, PolygonItem.Coordinates.Num());
				}
			}
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("No coordinates field found in geometry"));
	}

	return PolygonItems;
}
// 解析GeoJSON FeatureCollection，返回简化的数组结构
TArray<FSimpleGeoJsonItem> AYunnToPolygon::ParseGeoJsonToSimpleArray(
	const FString& GeoJsonString,
	const FString& PropertyKey1,
	const FString& PropertyKey2,
	const FString& PropertyKey3)
{
	TArray<FSimpleGeoJsonItem> SimpleArray;

	// 创建JSON读取器
	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(GeoJsonString);

	// 解析JSON字符串
	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to parse GeoJSON string"));
		return SimpleArray;
	}

	// 检查是否为FeatureCollection类型
	FString Type;
	if (JsonObject->TryGetStringField(TEXT("type"), Type))
	{
		if (Type == TEXT("FeatureCollection"))
		{
			// 解析FeatureCollection
			const TArray<TSharedPtr<FJsonValue>>* FeaturesArray;
			if (JsonObject->TryGetArrayField(TEXT("features"), FeaturesArray))
			{
				UE_LOG(LogTemp, Warning, TEXT("开始解析FeatureCollection，包含 %d 个Feature"), FeaturesArray->Num());

				for (int32 FeatureIndex = 0; FeatureIndex < FeaturesArray->Num(); FeatureIndex++)
				{
					const auto& FeatureValue = (*FeaturesArray)[FeatureIndex];
					const TSharedPtr<FJsonObject>* FeatureObject;

					if (FeatureValue->TryGetObject(FeatureObject) && FeatureObject->IsValid())
					{
						// 使用新的拆分函数，将多边形Feature拆分为多个独立项
						TArray<FSimpleGeoJsonItem> PolygonItems = SplitFeatureIntoPolygons(*FeatureObject, PropertyKey1, PropertyKey2, PropertyKey3);

						if (PolygonItems.Num() > 0)
						{
							// 添加所有拆分出的多边形项
							for (const auto& PolygonItem : PolygonItems)
							{
								SimpleArray.Add(PolygonItem);
							}
							UE_LOG(LogTemp, Log, TEXT("成功解析Feature %d: 拆分为 %d 个多边形"),
								FeatureIndex, PolygonItems.Num());
						}
						else
						{
							UE_LOG(LogTemp, Warning, TEXT("Feature %d 解析失败或无有效几何数据"), FeatureIndex);
						}
					}
					else
					{
						UE_LOG(LogTemp, Warning, TEXT("Feature %d 不是有效的JSON对象"), FeatureIndex);
					}
				}
			}
			else
			{
				UE_LOG(LogTemp, Error, TEXT("FeatureCollection中没有找到features数组"));
			}
		}
		else if (Type == TEXT("Feature"))
		{
			// 单个Feature，也使用拆分逻辑
			UE_LOG(LogTemp, Warning, TEXT("检测到单个Feature"));
			TArray<FSimpleGeoJsonItem> PolygonItems = SplitFeatureIntoPolygons(JsonObject, PropertyKey1, PropertyKey2, PropertyKey3);

			if (PolygonItems.Num() > 0)
			{
				// 添加所有拆分出的多边形项
				for (const auto& PolygonItem : PolygonItems)
				{
					SimpleArray.Add(PolygonItem);
				}
				UE_LOG(LogTemp, Warning, TEXT("单个Feature拆分为 %d 个多边形"), PolygonItems.Num());
			}
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("不支持的GeoJSON类型: %s"), *Type);
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("GeoJSON中没有找到type字段"));
	}

	UE_LOG(LogTemp, Warning, TEXT("简化数组解析完成，共 %d 个项目"), SimpleArray.Num());
	return SimpleArray;
}
// 从本地文件读取GeoJSON并解析为简化的数组结构
TArray<FSimpleGeoJsonItem> AYunnToPolygon::ParseGeoJsonFileToSimpleArray(
	const FString& FilePath,
	const FString& PropertyKey1,
	const FString& PropertyKey2,
	const FString& PropertyKey3)
{
	TArray<FSimpleGeoJsonItem> SimpleArray;

	// 检查文件是否存在
	if (!FPlatformFileManager::Get().GetPlatformFile().FileExists(*FilePath))
	{
		UE_LOG(LogTemp, Error, TEXT("GeoJSON文件不存在: %s"), *FilePath);
		return SimpleArray;
	}

	// 读取文件内容
	FString GeoJsonContent;
	if (!FFileHelper::LoadFileToString(GeoJsonContent, *FilePath))
	{
		UE_LOG(LogTemp, Error, TEXT("无法读取GeoJSON文件: %s"), *FilePath);
		return SimpleArray;
	}

	// 检查文件内容是否为空
	if (GeoJsonContent.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("GeoJSON文件内容为空: %s"), *FilePath);
		return SimpleArray;
	}

	UE_LOG(LogTemp, Warning, TEXT("成功读取GeoJSON文件: %s (大小: %d 字符)"), *FilePath, GeoJsonContent.Len());

	// 调用现有的字符串解析函数
	SimpleArray = ParseGeoJsonToSimpleArray(GeoJsonContent, PropertyKey1, PropertyKey2, PropertyKey3);

	UE_LOG(LogTemp, Warning, TEXT("文件解析完成，共获得 %d 个地块数据"), SimpleArray.Num());
	return SimpleArray;
}

