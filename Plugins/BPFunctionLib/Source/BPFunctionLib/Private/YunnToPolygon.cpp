// Fill out your copyright notice in the Description page of Project Settings.


#include "YunnToPolygon.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonReader.h"
#include "Engine/Engine.h"

// Sets default values
AYunnToPolygon::AYunnToPolygon()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void AYunnToPolygon::BeginPlay()
{
	Super::BeginPlay();

}

// Called every frame
void AYunnToPolygon::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}
// 解析GeoJSON字符串，提取MultiPolygon坐标和指定的properties属性
FGeoJsonParseResult AYunnToPolygon::ParseGeoJsonMultiPolygon(
	const FString& GeoJsonString,
	const FString& PropertyKey1,
	const FString& PropertyKey2,
	const FString& PropertyKey3)
{
	FGeoJsonParseResult Result;

	// 创建JSON读取器
	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(GeoJsonString);

	// 解析JSON字符串
	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to parse GeoJSON string"));
		return Result;
	}

	// 检查是否为Feature类型
	FString Type;
	if (!JsonObject->TryGetStringField(TEXT("type"), Type) || Type != TEXT("Feature"))
	{
		UE_LOG(LogTemp, Error, TEXT("GeoJSON is not a Feature type"));
		return Result;
	}

	// 解析properties
	const TSharedPtr<FJsonObject>* PropertiesObject;
	if (JsonObject->TryGetObjectField(TEXT("properties"), PropertiesObject) && PropertiesObject->IsValid())
	{
		// 提取自定义属性
		(*PropertiesObject)->TryGetStringField(PropertyKey1, Result.Property1);
		(*PropertiesObject)->TryGetStringField(PropertyKey2, Result.Property2);
		(*PropertiesObject)->TryGetStringField(PropertyKey3, Result.Property3);
	}

	// 解析geometry
	const TSharedPtr<FJsonObject>* GeometryObject;
	if (!JsonObject->TryGetObjectField(TEXT("geometry"), GeometryObject) || !GeometryObject->IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("No geometry field found in GeoJSON"));
		return Result;
	}

	// 检查geometry类型是否为MultiPolygon
	FString GeometryType;
	if (!(*GeometryObject)->TryGetStringField(TEXT("type"), GeometryType) || GeometryType != TEXT("MultiPolygon"))
	{
		UE_LOG(LogTemp, Error, TEXT("Geometry type is not MultiPolygon, found: %s"), *GeometryType);
		return Result;
	}

	// 解析coordinates
	const TArray<TSharedPtr<FJsonValue>>* CoordinatesArray;
	if ((*GeometryObject)->TryGetArrayField(TEXT("coordinates"), CoordinatesArray))
	{
		Result.Polygons = ParseMultiPolygonCoordinates(*CoordinatesArray);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("No coordinates field found in geometry"));
	}

	return Result;
}
// 辅助函数：解析坐标数组
TArray<FVector2D> AYunnToPolygon::ParseCoordinateArray(const TArray<TSharedPtr<FJsonValue>>& CoordArray)
{
	TArray<FVector2D> Coordinates;

	for (const auto& CoordValue : CoordArray)
	{
		const TArray<TSharedPtr<FJsonValue>>* CoordPair;
		if (CoordValue->TryGetArray(CoordPair) && CoordPair->Num() >= 2)
		{
			double X, Y;
			if ((*CoordPair)[0]->TryGetNumber(X) && (*CoordPair)[1]->TryGetNumber(Y))
			{
				Coordinates.Add(FVector2D(X, Y));
			}
		}
	}

	return Coordinates;
}

// 辅助函数：解析多边形环（一个多边形可能有多个环：外环+内环）
TArray<TArray<FVector2D>> AYunnToPolygon::ParsePolygonRings(const TArray<TSharedPtr<FJsonValue>>& RingsArray)
{
	TArray<TArray<FVector2D>> Rings;

	for (const auto& RingValue : RingsArray)
	{
		const TArray<TSharedPtr<FJsonValue>>* RingCoords;
		if (RingValue->TryGetArray(RingCoords))
		{
			TArray<FVector2D> Ring = ParseCoordinateArray(*RingCoords);
			if (Ring.Num() > 0)
			{
				Rings.Add(Ring);
			}
		}
	}

	return Rings;
}

// 辅助函数：解析MultiPolygon的所有多边形
TArray<TArray<TArray<FVector2D>>> AYunnToPolygon::ParseMultiPolygonCoordinates(const TArray<TSharedPtr<FJsonValue>>& MultiPolygonArray)
{
	TArray<TArray<TArray<FVector2D>>> MultiPolygon;

	for (const auto& PolygonValue : MultiPolygonArray)
	{
		const TArray<TSharedPtr<FJsonValue>>* PolygonRings;
		if (PolygonValue->TryGetArray(PolygonRings))
		{
			TArray<TArray<FVector2D>> Polygon = ParsePolygonRings(*PolygonRings);
			if (Polygon.Num() > 0)
			{
				MultiPolygon.Add(Polygon);
			}
		}
	}

	return MultiPolygon;
}

