// Fill out your copyright notice in the Description page of Project Settings.


#include "YunnToPolygon.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonReader.h"
#include "Engine/Engine.h"

// Sets default values
AYunnToPolygon::AYunnToPolygon()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void AYunnToPolygon::BeginPlay()
{
	Super::BeginPlay();

}

// Called every frame
void AYunnToPolygon::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}
// 解析GeoJSON字符串，提取MultiPolygon坐标和指定的properties属性
FGeoJsonParseResult AYunnToPolygon::ParseGeoJsonMultiPolygon(
	const FString& GeoJsonString,
	const FString& PropertyKey1,
	const FString& PropertyKey2,
	const FString& PropertyKey3)
{
	FGeoJsonParseResult Result;

	// 创建JSON读取器
	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(GeoJsonString);

	// 解析JSON字符串
	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to parse GeoJSON string"));
		return Result;
	}

	// 检查是否为Feature类型
	FString Type;
	if (!JsonObject->TryGetStringField(TEXT("type"), Type) || Type != TEXT("Feature"))
	{
		UE_LOG(LogTemp, Error, TEXT("GeoJSON is not a Feature type"));
		return Result;
	}

	// 解析properties
	const TSharedPtr<FJsonObject>* PropertiesObject;
	if (JsonObject->TryGetObjectField(TEXT("properties"), PropertiesObject) && PropertiesObject->IsValid())
	{
		// 提取自定义属性
		(*PropertiesObject)->TryGetStringField(PropertyKey1, Result.Property1);
		(*PropertiesObject)->TryGetStringField(PropertyKey2, Result.Property2);
		(*PropertiesObject)->TryGetStringField(PropertyKey3, Result.Property3);
	}

	// 解析geometry
	const TSharedPtr<FJsonObject>* GeometryObject;
	if (!JsonObject->TryGetObjectField(TEXT("geometry"), GeometryObject) || !GeometryObject->IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("No geometry field found in GeoJSON"));
		return Result;
	}

	// 检查geometry类型是否为MultiPolygon
	FString GeometryType;
	if (!(*GeometryObject)->TryGetStringField(TEXT("type"), GeometryType) || GeometryType != TEXT("MultiPolygon"))
	{
		UE_LOG(LogTemp, Error, TEXT("Geometry type is not MultiPolygon, found: %s"), *GeometryType);
		return Result;
	}

	// 解析coordinates
	const TArray<TSharedPtr<FJsonValue>>* CoordinatesArray;
	if ((*GeometryObject)->TryGetArrayField(TEXT("coordinates"), CoordinatesArray))
	{
		Result.Polygons = ParseMultiPolygonCoordinates(*CoordinatesArray);
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("No coordinates field found in geometry"));
	}

	return Result;
}
// 辅助函数：解析坐标数组
TArray<FVector2D> AYunnToPolygon::ParseCoordinateArray(const TArray<TSharedPtr<FJsonValue>>& CoordArray)
{
	TArray<FVector2D> Coordinates;

	for (const auto& CoordValue : CoordArray)
	{
		const TArray<TSharedPtr<FJsonValue>>* CoordPair;
		if (CoordValue->TryGetArray(CoordPair) && CoordPair->Num() >= 2)
		{
			double X, Y;
			if ((*CoordPair)[0]->TryGetNumber(X) && (*CoordPair)[1]->TryGetNumber(Y))
			{
				Coordinates.Add(FVector2D(X, Y));
			}
		}
	}

	return Coordinates;
}

// 辅助函数：解析多边形环（一个多边形可能有多个环：外环+内环）
TArray<FGeoJsonRing> AYunnToPolygon::ParsePolygonRings(const TArray<TSharedPtr<FJsonValue>>& RingsArray)
{
	TArray<FGeoJsonRing> Rings;

	for (int32 RingIndex = 0; RingIndex < RingsArray.Num(); RingIndex++)
	{
		const auto& RingValue = RingsArray[RingIndex];
		const TArray<TSharedPtr<FJsonValue>>* RingCoords;
		if (RingValue->TryGetArray(RingCoords))
		{
			TArray<FVector2D> Points = ParseCoordinateArray(*RingCoords);
			if (Points.Num() > 0)
			{
				FGeoJsonRing Ring;
				Ring.RingIndex = RingIndex;
				Ring.Points = Points;
				Rings.Add(Ring);
			}
		}
	}

	return Rings;
}

// 辅助函数：解析MultiPolygon的所有多边形
TArray<FGeoJsonPolygon> AYunnToPolygon::ParseMultiPolygonCoordinates(const TArray<TSharedPtr<FJsonValue>>& MultiPolygonArray)
{
	TArray<FGeoJsonPolygon> MultiPolygon;

	for (int32 PolygonIndex = 0; PolygonIndex < MultiPolygonArray.Num(); PolygonIndex++)
	{
		const auto& PolygonValue = MultiPolygonArray[PolygonIndex];
		const TArray<TSharedPtr<FJsonValue>>* PolygonRings;
		if (PolygonValue->TryGetArray(PolygonRings))
		{
			TArray<FGeoJsonRing> Rings = ParsePolygonRings(*PolygonRings);
			if (Rings.Num() > 0)
			{
				FGeoJsonPolygon Polygon;
				Polygon.PolygonIndex = PolygonIndex;
				Polygon.Rings = Rings;
				MultiPolygon.Add(Polygon);
			}
		}
	}

	return MultiPolygon;
}
// 辅助函数：解析单个Feature对象
FGeoJsonFeature AYunnToPolygon::ParseSingleFeature(
	const TSharedPtr<FJsonObject>& FeatureObject,
	const FString& PropertyKey1,
	const FString& PropertyKey2,
	const FString& PropertyKey3)
{
	FGeoJsonFeature Feature;

	if (!FeatureObject.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Invalid Feature object"));
		return Feature;
	}

	// 检查是否为Feature类型
	FString Type;
	if (!FeatureObject->TryGetStringField(TEXT("type"), Type) || Type != TEXT("Feature"))
	{
		UE_LOG(LogTemp, Warning, TEXT("Object is not a Feature type, found: %s"), *Type);
		return Feature;
	}

	// 解析properties
	const TSharedPtr<FJsonObject>* PropertiesObject;
	if (FeatureObject->TryGetObjectField(TEXT("properties"), PropertiesObject) && PropertiesObject->IsValid())
	{
		// 提取自定义属性
		(*PropertiesObject)->TryGetStringField(PropertyKey1, Feature.Property1);
		(*PropertiesObject)->TryGetStringField(PropertyKey2, Feature.Property2);
		(*PropertiesObject)->TryGetStringField(PropertyKey3, Feature.Property3);
	}

	// 解析geometry
	const TSharedPtr<FJsonObject>* GeometryObject;
	if (!FeatureObject->TryGetObjectField(TEXT("geometry"), GeometryObject) || !GeometryObject->IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("No geometry field found in Feature"));
		return Feature;
	}

	// 检查geometry类型是否为MultiPolygon
	FString GeometryType;
	if (!(*GeometryObject)->TryGetStringField(TEXT("type"), GeometryType) || GeometryType != TEXT("MultiPolygon"))
	{
		UE_LOG(LogTemp, Warning, TEXT("Geometry type is not MultiPolygon, found: %s"), *GeometryType);
		return Feature;
	}

	// 解析coordinates
	const TArray<TSharedPtr<FJsonValue>>* CoordinatesArray;
	if ((*GeometryObject)->TryGetArrayField(TEXT("coordinates"), CoordinatesArray))
	{
		Feature.Polygons = ParseMultiPolygonCoordinates(*CoordinatesArray);
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("No coordinates field found in geometry"));
	}

	return Feature;
}
// 解析GeoJSON FeatureCollection，提取所有Feature的MultiPolygon坐标和指定的properties属性
FGeoJsonCollectionResult AYunnToPolygon::ParseGeoJsonFeatureCollection(
	const FString& GeoJsonString,
	const FString& PropertyKey1,
	const FString& PropertyKey2,
	const FString& PropertyKey3)
{
	FGeoJsonCollectionResult Result;

	// 创建JSON读取器
	TSharedPtr<FJsonObject> JsonObject;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(GeoJsonString);

	// 解析JSON字符串
	if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to parse GeoJSON string"));
		return Result;
	}

	// 检查是否为FeatureCollection类型
	FString Type;
	if (JsonObject->TryGetStringField(TEXT("type"), Type))
	{
		if (Type == TEXT("FeatureCollection"))
		{
			// 解析FeatureCollection
			const TArray<TSharedPtr<FJsonValue>>* FeaturesArray;
			if (JsonObject->TryGetArrayField(TEXT("features"), FeaturesArray))
			{
				Result.TotalFeatures = FeaturesArray->Num();
				UE_LOG(LogTemp, Warning, TEXT("开始解析FeatureCollection，包含 %d 个Feature"), Result.TotalFeatures);

				for (int32 FeatureIndex = 0; FeatureIndex < FeaturesArray->Num(); FeatureIndex++)
				{
					const auto& FeatureValue = (*FeaturesArray)[FeatureIndex];
					const TSharedPtr<FJsonObject>* FeatureObject;

					if (FeatureValue->TryGetObject(FeatureObject) && FeatureObject->IsValid())
					{
						FGeoJsonFeature Feature = ParseSingleFeature(*FeatureObject, PropertyKey1, PropertyKey2, PropertyKey3);

						// 检查Feature是否解析成功（至少有一个多边形）
						if (Feature.Polygons.Num() > 0)
						{
							Result.Features.Add(Feature);
							Result.SuccessfulFeatures++;
							UE_LOG(LogTemp, Log, TEXT("成功解析Feature %d: %s"), FeatureIndex, *Feature.Property1);
						}
						else
						{
							Result.FailedFeatures++;
							UE_LOG(LogTemp, Warning, TEXT("Feature %d 解析失败或无有效几何数据"), FeatureIndex);
						}
					}
					else
					{
						Result.FailedFeatures++;
						UE_LOG(LogTemp, Warning, TEXT("Feature %d 不是有效的JSON对象"), FeatureIndex);
					}
				}
			}
			else
			{
				UE_LOG(LogTemp, Error, TEXT("FeatureCollection中没有找到features数组"));
			}
		}
		else if (Type == TEXT("Feature"))
		{
			// 单个Feature，使用原有函数解析
			UE_LOG(LogTemp, Warning, TEXT("检测到单个Feature，建议使用ParseGeoJsonMultiPolygon函数"));
			FGeoJsonParseResult SingleResult = ParseGeoJsonMultiPolygon(GeoJsonString, PropertyKey1, PropertyKey2, PropertyKey3);

			if (SingleResult.Polygons.Num() > 0)
			{
				FGeoJsonFeature Feature;
				Feature.Property1 = SingleResult.Property1;
				Feature.Property2 = SingleResult.Property2;
				Feature.Property3 = SingleResult.Property3;
				Feature.Polygons = SingleResult.Polygons;

				Result.Features.Add(Feature);
				Result.TotalFeatures = 1;
				Result.SuccessfulFeatures = 1;
				Result.FailedFeatures = 0;
			}
			else
			{
				Result.TotalFeatures = 1;
				Result.SuccessfulFeatures = 0;
				Result.FailedFeatures = 1;
			}
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("不支持的GeoJSON类型: %s"), *Type);
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("GeoJSON中没有找到type字段"));
	}

	UE_LOG(LogTemp, Warning, TEXT("解析完成: 总计 %d 个Feature，成功 %d 个，失败 %d 个"),
		Result.TotalFeatures, Result.SuccessfulFeatures, Result.FailedFeatures);

	return Result;
}
// 解析GeoJSON FeatureCollection，返回简化的数组结构
TArray<FSimpleGeoJsonItem> AYunnToPolygon::ParseGeoJsonToSimpleArray(
	const FString& GeoJsonString,
	const FString& PropertyKey1,
	const FString& PropertyKey2,
	const FString& PropertyKey3)
{
	TArray<FSimpleGeoJsonItem> SimpleArray;

	// 首先使用现有函数解析完整数据
	FGeoJsonCollectionResult CollectionResult = ParseGeoJsonFeatureCollection(
		GeoJsonString, PropertyKey1, PropertyKey2, PropertyKey3);

	UE_LOG(LogTemp, Warning, TEXT("开始转换为简化数组格式，共 %d 个Feature"), CollectionResult.Features.Num());

	// 将每个Feature转换为简化格式
	for (int32 FeatureIndex = 0; FeatureIndex < CollectionResult.Features.Num(); FeatureIndex++)
	{
		const auto& Feature = CollectionResult.Features[FeatureIndex];
		FSimpleGeoJsonItem SimpleItem;

		// 复制属性值
		SimpleItem.Property1 = Feature.Property1;
		SimpleItem.Property2 = Feature.Property2;
		SimpleItem.Property3 = Feature.Property3;

		// 扁平化所有坐标点到单个数组
		for (const auto& Polygon : Feature.Polygons)
		{
			for (const auto& Ring : Polygon.Rings)
			{
				// 将所有点添加到坐标数组中
				for (const FVector2D& Point : Ring.Points)
				{
					SimpleItem.Coordinates.Add(Point);
				}
			}
		}

		// 添加到结果数组
		SimpleArray.Add(SimpleItem);

		UE_LOG(LogTemp, Log, TEXT("Feature %d 转换完成: %s, 坐标点数量: %d"),
			FeatureIndex, *SimpleItem.Property1, SimpleItem.Coordinates.Num());
	}

	UE_LOG(LogTemp, Warning, TEXT("简化数组转换完成，共 %d 个项目"), SimpleArray.Num());
	return SimpleArray;
}

