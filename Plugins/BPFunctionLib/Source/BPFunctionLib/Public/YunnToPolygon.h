// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Engine/Engine.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonReader.h"
#include "YunnToPolygon.generated.h"

// 结构体用于存储单个多边形环的数据
USTRUCT(BlueprintType)
struct BPFUNCTIONLIB_API FGeoJsonRing
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	int32 RingIndex;  // 环索引，0为外环，>0为内环

	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	TArray<FVector2D> Points;  // 环的坐标点

	FGeoJsonRing()
	{
		RingIndex = 0;
	}
};

// 结构体用于存储单个多边形的数据
USTRUCT(BlueprintType)
struct BPFUNCTIONLIB_API FGeoJsonPolygon
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	int32 PolygonIndex;  // 多边形索引

	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	TArray<FGeoJsonRing> Rings;  // 多边形的所有环

	FGeoJsonPolygon()
	{
		PolygonIndex = 0;
	}
};

// 结构体用于存储单个Feature的解析结果
USTRUCT(BlueprintType)
struct BPFUNCTIONLIB_API FGeoJsonFeature
{
	GENERATED_BODY()

	// 从properties中提取的三个自定义属性值
	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	FString Property1;

	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	FString Property2;

	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	FString Property3;

	// MultiPolygon的所有多边形数据
	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	TArray<FGeoJsonPolygon> Polygons;

	FGeoJsonFeature()
	{
		Property1 = TEXT("");
		Property2 = TEXT("");
		Property3 = TEXT("");
	}
};

// 结构体用于存储GeoJSON解析结果（单个Feature）
USTRUCT(BlueprintType)
struct BPFUNCTIONLIB_API FGeoJsonParseResult
{
	GENERATED_BODY()

	// 从properties中提取的三个自定义属性值
	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	FString Property1;

	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	FString Property2;

	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	FString Property3;

	// MultiPolygon的所有多边形数据
	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	TArray<FGeoJsonPolygon> Polygons;

	FGeoJsonParseResult()
	{
		Property1 = TEXT("");
		Property2 = TEXT("");
		Property3 = TEXT("");
	}
};

// 结构体用于存储FeatureCollection的解析结果（多个Feature）
USTRUCT(BlueprintType)
struct BPFUNCTIONLIB_API FGeoJsonCollectionResult
{
	GENERATED_BODY()

	// 所有Feature的数据
	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	TArray<FGeoJsonFeature> Features;

	// 解析统计信息
	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	int32 TotalFeatures;

	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	int32 SuccessfulFeatures;

	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	int32 FailedFeatures;

	FGeoJsonCollectionResult()
	{
		TotalFeatures = 0;
		SuccessfulFeatures = 0;
		FailedFeatures = 0;
	}
};

// 简化的数据项结构体 - 包含三个属性值和坐标数组
USTRUCT(BlueprintType)
struct BPFUNCTIONLIB_API FSimpleGeoJsonItem
{
	GENERATED_BODY()

	// 三个自定义属性值
	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	FString Property1;

	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	FString Property2;

	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	FString Property3;

	// 扁平化的坐标数组 - 所有多边形的所有环的所有点
	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	TArray<FVector2D> Coordinates;

	FSimpleGeoJsonItem()
	{
		Property1 = TEXT("");
		Property2 = TEXT("");
		Property3 = TEXT("");
	}
};

UCLASS()
class BPFUNCTIONLIB_API AYunnToPolygon : public AActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AYunnToPolygon();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;
	// 解析单个GeoJSON Feature，提取MultiPolygon坐标和指定的properties属性
	UFUNCTION(BlueprintCallable, Category = "YunnToPolygon")
	static FGeoJsonParseResult ParseGeoJsonMultiPolygon(
		const FString& GeoJsonString,
		const FString& PropertyKey1,
		const FString& PropertyKey2,
		const FString& PropertyKey3
	);

	// 解析GeoJSON FeatureCollection，提取所有Feature的MultiPolygon坐标和指定的properties属性
	UFUNCTION(BlueprintCallable, Category = "YunnToPolygon")
	static FGeoJsonCollectionResult ParseGeoJsonFeatureCollection(
		const FString& GeoJsonString,
		const FString& PropertyKey1,
		const FString& PropertyKey2,
		const FString& PropertyKey3
	);

	// 解析GeoJSON FeatureCollection，返回简化的数组结构（每个元素包含三个属性值和坐标数组）
	UFUNCTION(BlueprintCallable, Category = "YunnToPolygon")
	static TArray<FSimpleGeoJsonItem> ParseGeoJsonToSimpleArray(
		const FString& GeoJsonString,
		const FString& PropertyKey1,
		const FString& PropertyKey2,
		const FString& PropertyKey3
	);

private:
	// 辅助函数：解析坐标数组
	static TArray<FVector2D> ParseCoordinateArray(const TArray<TSharedPtr<FJsonValue>>& CoordArray);

	// 辅助函数：解析多边形环（一个多边形可能有多个环：外环+内环）
	static TArray<FGeoJsonRing> ParsePolygonRings(const TArray<TSharedPtr<FJsonValue>>& RingsArray);

	// 辅助函数：解析MultiPolygon的所有多边形
	static TArray<FGeoJsonPolygon> ParseMultiPolygonCoordinates(const TArray<TSharedPtr<FJsonValue>>& MultiPolygonArray);

	// 辅助函数：解析单个Feature对象
	static FGeoJsonFeature ParseSingleFeature(
		const TSharedPtr<FJsonObject>& FeatureObject,
		const FString& PropertyKey1,
		const FString& PropertyKey2,
		const FString& PropertyKey3
	);





};
