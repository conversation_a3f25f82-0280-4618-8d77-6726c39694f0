// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Engine/Engine.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonReader.h"
#include "YunnToPolygon.generated.h"



// 简化的数据项结构体 - 包含三个属性值和坐标数组
USTRUCT(BlueprintType)
struct BPFUNCTIONLIB_API FSimpleGeoJsonItem
{
	GENERATED_BODY()

	// 三个自定义属性值
	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	FString Property1;

	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	FString Property2;

	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	FString Property3;

	// 扁平化的坐标数组 - 所有多边形的所有环的所有点
	UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
	TArray<FVector2D> Coordinates;

	FSimpleGeoJsonItem()
	{
		Property1 = TEXT("");
		Property2 = TEXT("");
		Property3 = TEXT("");
	}
};

UCLASS()
class BPFUNCTIONLIB_API AYunnToPolygon : public AActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AYunnToPolygon();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;


	// 解析GeoJSON FeatureCollection，返回简化的数组结构（每个元素包含三个属性值和坐标数组）
	UFUNCTION(BlueprintCallable, Category = "YunnToPolygon")
	static TArray<FSimpleGeoJsonItem> ParseGeoJsonToSimpleArray(
		const FString& GeoJsonString,
		const FString& PropertyKey1,
		const FString& PropertyKey2,
		const FString& PropertyKey3
	);

	// 从本地文件读取GeoJSON并解析为简化的数组结构
	UFUNCTION(BlueprintCallable, Category = "YunnToPolygon")
	static TArray<FSimpleGeoJsonItem> ParseGeoJsonFileToSimpleArray(
		const FString& FilePath,
		const FString& PropertyKey1,
		const FString& PropertyKey2,
		const FString& PropertyKey3
	);

private:
	// 辅助函数：解析坐标数组
	static TArray<FVector2D> ParseCoordinateArray(const TArray<TSharedPtr<FJsonValue>>& CoordArray);

	// 辅助函数：解析单个Feature对象并提取坐标
	static FSimpleGeoJsonItem ParseSingleFeatureToSimple(
		const TSharedPtr<FJsonObject>& FeatureObject,
		const FString& PropertyKey1,
		const FString& PropertyKey2,
		const FString& PropertyKey3
	);

	// 辅助函数：将单个Feature拆分为多个多边形项
	static TArray<FSimpleGeoJsonItem> SplitFeatureIntoPolygons(
		const TSharedPtr<FJsonObject>& FeatureObject,
		const FString& PropertyKey1,
		const FString& PropertyKey2,
		const FString& PropertyKey3
	);





};
