// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CesiumGeoreference.h"
#include "GameFramework/Actor.h"
#include "YunnToPOI.generated.h"


USTRUCT(BlueprintType)
struct FPOIAttributes
{
	GENERATED_USTRUCT_BODY()


    //--------------------通用标签样式------------------------//
	//POI id
	UPROPERTY(BlueprintReadWrite)
	int32 PA_Id;

	//POI位置 
	UPROPERTY(BlueprintReadWrite)
	FVector PA_Location;

	//是否使用widget  默认true
	UPROPERTY(BlueprintReadWrite)
	bool PA_UsedWidget;

	//POI 类型    0-4   0-横向标签  1-竖向标签   2-GIF标签   3-楼栋标签  默认0  4-拉进显示文字标签
	UPROPERTY(BlueprintReadWrite)
	uint8 PA_WidgetStyle;

	//是否显示标签小图标    默认true
	UPROPERTY(BlueprintReadWrite)
	bool PA_ShowHeaderImage;

	//标签小图标   url
	UPROPERTY(BlueprintReadWrite)
	FString PA_HeaderImage;

	//背景图前段    url
	UPROPERTY(BlueprintReadWrite)
	FString PA_BackgroundImage1;

	//背景图中段     url
	UPROPERTY(BlueprintReadWrite)
	FString PA_BackgroundImage2;

	//背景图后段     url
	UPROPERTY(BlueprintReadWrite)
	FString PA_BackgroundImage3;

	//POI内容 
	UPROPERTY(BlueprintReadWrite)
	FString PA_Name;

	//文字样式     默认0 暂时只有一种sourcehansanssc-bold_Font
	UPROPERTY(BlueprintReadWrite)
	uint8 PA_FontStyle;

	//文字颜色     RGBA (R=1.000000,G=0.300000,B=0.340000,A=0.560000)
	UPROPERTY(BlueprintReadWrite)
	FLinearColor PA_FontColor;

	//文字放大缩小    默认2
	UPROPERTY(BlueprintReadWrite)
	float  PA_FontEnlargement;


	//是否可以点击  默认false
	UPROPERTY(BlueprintReadWrite)
	bool PA_CanClick;

	//点击图片     url
	UPROPERTY(BlueprintReadWrite)
	FString PA_ClickImage;


	//POI大小缩放   默认1.5
	UPROPERTY(BlueprintReadWrite)
	float PA_WidgetBrushSizeNum;

	//标签小图标大小缩放    默认1
	UPROPERTY(BlueprintReadWrite)
	float PA_HeaderImageSizeNum;

	//点击标签缩放     默认0.7
	UPROPERTY(BlueprintReadWrite)
	float PA_ClickImageSizeNum;


	//--------------------标签线和动画样式(通用)------------------------//

	//标签线图片    url
	UPROPERTY(BlueprintReadWrite)
	FString PA_LineImage;

	//标签线长度   默认120
	UPROPERTY(BlueprintReadWrite)
	float PA_LineLength;

	//标签线宽度     默认2
	UPROPERTY(BlueprintReadWrite)
	float PA_LineWidth;

	//标签线上的点图片    url
	UPROPERTY(BlueprintReadWrite)
	FString PA_LinePointImage;

	//标签线上的点图片大小     默认20
	UPROPERTY(BlueprintReadWrite)
    float PA_LinePointImageSizeNum;

	//POI 动画类型(剖面样式)  默认是1   0-6   0-None  1-OpenAlpha  2-OpenScale   3-OpenLeftToRight  4-OpenRightToLeft  5-OpenTopToDown  6-OpenDownToTop
	UPROPERTY(BlueprintReadWrite)
	uint8 PA_OpenType;

	//POI 锚点  默认0，0
	UPROPERTY(BlueprintReadWrite)
	FVector2D PA_InPoivt;

	//动画时间  默认1
	UPROPERTY(BlueprintReadWrite)
	float PA_AnimTime;


	//--------------------横向标签设置------------------------//

	//横向标签镜像  默认false
	UPROPERTY(BlueprintReadWrite)
	bool PA_HorizontalMirror;

	//标签线旋转  范围 -85 ~ 85  默认0
	UPROPERTY(BlueprintReadWrite)
	float PA_HorizontalLineRotation;

	//标签线位置  范围 0 ~ 1   0左 右1  默认0.5
	UPROPERTY(BlueprintReadWrite)
	float PA_HorizontalLinePosition;

	//前段留白   0~2  默认0.9
	UPROPERTY(BlueprintReadWrite)
	float PA_HorizontalLineLeftBlank;

	//尾段留白   0~2  默认0.35
	UPROPERTY(BlueprintReadWrite)
	float PA_HorizontalLineRightBlank;

	//文字距离小图标距离  0~1  0.253
	UPROPERTY(BlueprintReadWrite)
	float PA_HeaderImageTextDistance;

	//标签距离标签线距离  默认 3
	UPROPERTY(BlueprintReadWrite)
    float PA_LineWidgetDistance;

	//文字上下间距调整   正数向上   默认5
	UPROPERTY(BlueprintReadWrite)
	float PA_TextBottomDistance;

	//--------------------竖向标签设置------------------------//
	//竖向标签线距离  默认-19
	UPROPERTY(BlueprintReadWrite)
	float  PA_VerticalLineWidgetDistance;

	//开启竖向标签镜像 默认 false
    UPROPERTY(BlueprintReadWrite)
	bool PA_VerticalMirror;

	//竖向标签顶部留白  0~1 默认0.77
    UPROPERTY(BlueprintReadWrite)
	float PA_VerticalLineTopBlank;

    //竖向标签底部留白   0~1 默认0.5
    UPROPERTY(BlueprintReadWrite)
	float PA_VerticalLineBottomBlank;

    //竖向标签线距离顶部距离    0~1 默认0
	UPROPERTY(BlueprintReadWrite)
    float PA_VerticalLineDistanceTop;

	//--------------------标签tag------------------------//
	//POITag 
	UPROPERTY(BlueprintReadWrite)
	TArray<FString> PA_Tags;

};

// 委托声明：异步加载图片成功时回调
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTextureLoaded, UTexture2D*, LoadedTexture, FString, ImageUrl);


UCLASS()
class BPFUNCTIONLIB_API AYunnToPOI : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	AYunnToPOI();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:	
	// Called every frame
	virtual void Tick(float DeltaTime) override;


	//解析POI数据 转换成键值对
	UFUNCTION(BlueprintCallable, Category = "YunnToPOI")
	void ConvertJsonToPOIAttributes(TMap<FString, FPOIAttributes>& OutAmenitiesMap,FString POIJson);

	//自定义高度
	UPROPERTY(BlueprintReadWrite, Category = "YunnToPOI")
	float UnrealHeight;

	//cesium
	UPROPERTY(BlueprintReadWrite, Category = "YunnToPOI")
	ACesiumGeoreference* CesiumGeoreference;


public:
	UPROPERTY(BlueprintAssignable, Category = "YunnToPOI")
	FOnTextureLoaded OnTextureLoaded;
	// 输入URL并返回 UTexture2D
		UFUNCTION(BlueprintCallable, Category = "YunnToPOI")
	void LoadTextureFromURL(const FString & ImagePathOrURL);

private:
	// 图片下载并转换为 UTexture2D
	UTexture2D* LoadTextureFromBytes(const TArray<uint8>& ImageData);






	//修改本地poi位置
public:
	UFUNCTION(BlueprintCallable, Category = "YunnToPOI")
	bool SetLocalPOILocation(const FString& JsonFilePath, int32 ItemId, const FString& NewLocationX, const FString& NewLocationY, const FString& NewLocationZ);
private:
	// 位置修改的JSON函数
	bool ModifyLocationInJsonArray(TArray<TSharedPtr<FJsonValue>>& JsonArray, int32 TargetId, const FString& NewLocationX, const FString& NewLocationY, const FString& NewLocationZ, bool& bFound);



};
