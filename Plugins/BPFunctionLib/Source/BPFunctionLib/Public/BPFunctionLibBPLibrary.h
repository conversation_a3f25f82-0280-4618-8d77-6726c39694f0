// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Kismet/BlueprintFunctionLibrary.h"
#include "Styling/SlateTypes.h"
#include "Engine/LevelStreamingDynamic.h"
#include "Engine/LevelStreaming.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "BPFunctionLibBPLibrary.generated.h"


/*
*	Function library class.
*	Each function in it is expected to be static and represents blueprint node that can be called in any blueprint.
*
*	When declaring function you can define metadata for the node. Key function specifiers will be BlueprintPure and BlueprintCallable.
*	BlueprintPure - means the function does not affect the owning object in any way and thus creates a node without Exec pins.
*	BlueprintCallable - makes a function which can be executed in Blueprints - Thus it has Exec pins.
*	DisplayName - full name of the node, shown when you mouse over the node and in the blueprint drop down menu.
*				Its lets you name the node using characters not allowed in C++ function names.
*	CompactNodeTitle - the word(s) that appear on the node.
*	Keywords -	the list of keywords that helps you to find node when you search for it using Blueprint drop-down menu.
*				Good example is "Print String" node which you can find also by using keyword "log".
*	Category -	the category your node will be under in the Blueprint drop-down menu.
*
*	For more info on custom blueprint nodes visit documentation:
*	https://wiki.unrealengine.com/Custom_Blueprint_Node_Creation
*/



USTRUCT(BlueprintType)
struct FPolygonData
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite)
	int32 PolygonIndex;

	UPROPERTY(BlueprintReadWrite)
	TArray<FVector2D> Points;
};


UCLASS()
class UBPFunctionLibBPLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_UCLASS_BODY()

	UFUNCTION(BlueprintCallable, meta = (DisplayName = "Execute Sample function", Keywords = "BPFunctionLib sample test testing"), Category = "BPFunctionLib")
	static float BPFunctionLibSampleFunction(float Param);

	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
	static FButtonStyle LoadImageAndCreateButtonStyle(FString LoadPath);

	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
	static FString MD5Encryption(FString SourceStr);

	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
		static void OpenExe(FString FilePath);

	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
		static void CloseExe(FString ProcessName);

	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
		static void SetComponentAffectDistanceFieldLighting(UPrimitiveComponent* theComponent, bool bIsAffectDistanceFieldLighting);

	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
		static void ReadStringFromText(FString& Result, const FString TextPath);

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetIntranetIp();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetLocalHostName();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetHardwareID();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetOSName();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetCPUName();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetGPUName();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static FString GetOSID();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static bool Is64BitOS();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
		static void GetScreenSize(int32& X, int32& Y);

	private:

	static void DoChangeLevelTransform(const ULevelStreaming* StreamingLevel);
	static void RemoveLevelTransform(const ULevelStreaming* StreamingLevel);
	static void ApplyLevelTransform(ULevel* Level, const FTransform& LevelTransform);




	//csv
#pragma region LoadCSVFile
	//  加载csv文件
public:
	UFUNCTION(BlueprintCallable,Category = "BPFunctionLib")
	static TArray<FString> LoadCSVFile(const FString& FilePath);

private:

	// 文件编码是否为ANSI
	UFUNCTION()
	static bool isANSI(const FString& FilePath);

	// csv转换为String
	UFUNCTION()
	static void CsvBufferToString(FString& Result, const TArray<uint8>& Buffer, const FString& FilePath);

	//编码转换为UTF-8
	UFUNCTION()
	static bool ConvertToUTF8(const FString& FilePath);

#pragma endregion
	//结束csv功能



/*
	//周边配套json文件写入读取
#pragma region SurroundingAmenities

public:
	//加载本地json文件
	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
	static FString LoadJsonToString(const FString& FilePath);

	//写入本地json文件
	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
	static bool ExportInfoToJson(const TArray<FSurroundingAmenities>& SA_Infos);

   //读取json文件转换为键值对
	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
	static bool ImportInfoFromJson(TMap<FString, FProcessedSurroundingAmenities>& OutAmenitiesMap);



#pragma endregion
	//结束周边配套json文件写入读取功能
*/



public:

	UFUNCTION(BlueprintCallable, meta = (WorldContext = "WorldContextObject", CompactNodeTitle = "UpdateLevelTransform", DisplayName = "UpdateLevelTransform", Keywords = "Update Level Transform"), Category = "YeHaike|LevelController|Functions")
	static void UpdateLevelTransform(ULevelStreaming* StreamingLevel, const FTransform& Transform);

	// Is playing in editor or not.
	UFUNCTION(BlueprintPure, meta = (WorldContext = "WorldContextObject", CompactNodeTitle = "IsPIE", DisplayName = "IsPIE", Keywords = "Is PIE"), Category = "YeHaike|LevelController|Functions")
	static bool IsPIE()
	{
	#if WITH_EDITOR
		return true;
	#else
		return false;
	#endif
	}


#pragma region Crypto

private:
	static const int32 MAX_COMMAND_SIZE = 10000;

	static void AdjustString(const char* str, int pos, char* buf);
	static bool GetMacAddress(int nNetIndex, char* sAddress);
	static ULONG GetHDSerial(char* pszIDBuff, int nBuffLen, int nDriveID);




public:
	//获取网卡地址
	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
	static FString GetUniqueMacAddress();


	//获取硬盘序列号
	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
	static TArray<FString>  GetHDSerial();

	//获取CPU序列号
	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
	static FString GetCPUAddresss();

	//获取主板序列号
	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
	static FString GetMotherboardSerial();

	UFUNCTION(BlueprintPure, Category = "BPFunctionLib")
	static FString GetYunnToUniqueID();

#pragma endregion


#pragma region MoviesCrypto

	//视频加密
	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
	static void EncryptVideoFile(const FString& FilePath, const FString& DestinationFolderPath, uint8 Key);


	//获取所有视频
	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
	static TArray<FString> GetMP4FilesInDirectory(const FString& Directory);

	//删除文件夹
	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
	static bool DeleteDirectory(const FString& Directory);


#pragma endregion


	//解析wkt
	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
	static TArray<FVector2D> ParseWKT(const FString& WKT);
	//wkt多个地块
	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
	static TArray<FPolygonData> ParseWKT1(const FString& WKT);



	//复制粘贴
#pragma region CopyVariables


	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
	static void CopyMessageToClipboard(FString text);

	UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
	static FString PasteMessageFromClipboard();


#pragma endregion






//本地读取
#pragma region LocalLoading

	public:
		//加载本地json文件
		UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
		static FString LoadJsonToString(const FString& FilePath);

		//获取所有图片路径
		UFUNCTION(BlueprintCallable, Category = "BPFunctionLib")
		static TArray<FString> GetPngFilesInDirectory(const FString& Directory);


#pragma endregion




};
