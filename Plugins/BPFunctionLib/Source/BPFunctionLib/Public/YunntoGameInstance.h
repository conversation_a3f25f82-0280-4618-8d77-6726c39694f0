// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/GameInstance.h"
#include "TcpSocket.h"
#include <string>
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"
#include "YunntoGameInstance.generated.h"

/*
namespace crypt_unit {

	//随机生成8个字符的字符串
	std::string randomkey();

	//获取dh换算值
	std::string dhexchange(const std::string& in);

	//获取dh密钥
	std::string dhsecret(const std::string& in1, const std::string& in2);

	//哈希加密
	std::string hmac64(const std::string& in1, const std::string& in2);

	//b64 编码
	std::string base64encode(const std::string& in);

	//b64 解码
	std::string base64decode(const std::string& in);

	//密钥转成字节流(用于显示密钥是什么)
	std::string hexencode(const std::string& in);

	//des 编码
	std::string desencode(const std::string& in1, const std::string& in2);

	//des 解码
	std::string desdecode(const std::string& in1, const std::string& in2);


};
*/
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FServerDisconnectedDelegate);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FNotInWhiteListDelegate);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FProjectDueDelegate);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FProjectPauseDelegate);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FExpireHourDelegate, int32, ReHours);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FProjectLoginSuccessDelegate);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FProjectLogoStateDelegate, int32, State);

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnRefreshCameraParameters);

/**
 * 
 */
UCLASS()
class BPFUNCTIONLIB_API UYunntoGameInstance : public UGameInstance
{
	GENERATED_BODY()

public:

	void Init() override;

	void Shutdown() override;

	UFUNCTION(BlueprintCallable, Category = "Net")
	void StartConnectSever();

protected:
	UPROPERTY(EditDefaultsOnly, Category = Default)
	FString ipAddress;
	
	UPROPERTY(EditDefaultsOnly, Category = Default)
	int32 port;

	FString MacID;

	UPROPERTY()
	class ATcpSocket* TcpSocketActor = nullptr;

	FTcpSocketDisconnectDelegate OnServerDisconnected;
	FTcpSocketConnectDelegate OnServerConnected;
	FTcpSocketReceivedMessageDelegate OnServerMessageReceived;

	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite, Category = Default)
	FString ProjectName;

	UFUNCTION()
	void ExecuteOnServerDisconnected();

	UFUNCTION()
	void ExecuteOnServerConnected();

	UFUNCTION()
	void ExecuteOnServerMessageReceived(TArray<uint8>& Message);

	UPROPERTY(BlueprintAssignable, Category = "Net")
	FServerDisconnectedDelegate NotifyServerDisconnected;

	UPROPERTY(BlueprintAssignable, Category = "Net")
	FNotInWhiteListDelegate NotifyNotInWhiteList;

	UPROPERTY(BlueprintAssignable, Category = "Net")
	FProjectPauseDelegate NotifyProjectPause;

	UPROPERTY(BlueprintAssignable, Category = "Net")
	FProjectDueDelegate NotifyProjectDue;

	UPROPERTY(BlueprintAssignable, Category = "Net")
	FExpireHourDelegate NotifyExpireHour;

	UPROPERTY(BlueprintAssignable, Category = "Net")
	FExpireHourDelegate NotifyExpireHourUpdate;

	UPROPERTY(BlueprintAssignable, Category = "Net")
	FProjectLoginSuccessDelegate NotifyLoginSuccess;

	UPROPERTY(BlueprintAssignable, Category = "Net")
	FProjectLogoStateDelegate NotifyLogoState;


	UPROPERTY(BlueprintAssignable, Category = "Net")
	FOnRefreshCameraParameters OnRefreshAllCamera;

	
private:
	std::string cstrChallenge;
	std::string cstrClientkey;
	std::string cstrSkey64;
	bool bIsSuccessLogin;

	FTimerHandle TH_SendHeartbeatPak;

	void DataAnalysis(TArray<uint8>& Message, uint32_t len);

	// 静态变量用于跟踪当前通知
	static TSharedPtr<SNotificationItem> CurrentNotificationItem;


public:
	UFUNCTION(BlueprintImplementableEvent,Exec)
	void YunnToManager();

	// 显示编辑器通知消息（右下角）
	UFUNCTION(BlueprintCallable, Category = "YunntoGameInstance")
	void ShowEditorNotification(const FString& Message);


	UFUNCTION(BlueprintImplementableEvent)
	void ceshi();
 

};
