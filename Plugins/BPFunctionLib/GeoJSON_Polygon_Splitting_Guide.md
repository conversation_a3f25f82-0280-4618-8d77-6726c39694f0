# GeoJSON 多边形拆分功能使用指南

## 🎯 功能概述

现在 `ParseGeoJsonToSimpleArray` 函数具备了**智能多边形拆分功能**：

- ✅ **自动检测**: 检测MultiPolygon中包含的多边形数量
- ✅ **智能拆分**: 将包含多个多边形的Feature拆分为多个独立的结构体项
- ✅ **索引标识**: 在Property1后面添加 `*索引` 来区分不同的多边形
- ✅ **属性保持**: Property2和Property3保持原值不变

## 📊 拆分逻辑

### 单个多边形的Feature
```json
{
  "type": "Feature",
  "properties": { "owner": "业主A", "remark": "国有", "serial_number": "123" },
  "geometry": {
    "type": "MultiPolygon",
    "coordinates": [ [[[坐标点...]]] ]  // 只有1个多边形
  }
}
```
**输出**: 1个结构体项
- Property1: "业主A*0"
- Property2: "国有"
- Property3: "123"

### 多个多边形的Feature
```json
{
  "type": "Feature", 
  "properties": { "owner": "广州铁路中心医院", "remark": "国有", "serial_number": "268" },
  "geometry": {
    "type": "MultiPolygon",
    "coordinates": [
      [[[第一个多边形坐标...]]],  // 第一个多边形
      [[[第二个多边形坐标...]]]   // 第二个多边形
    ]
  }
}
```
**输出**: 2个结构体项
- 项目1: Property1="广州铁路中心医院*0", Property2="国有", Property3="268"
- 项目2: Property1="广州铁路中心医院*1", Property2="国有", Property3="268"

## 🚀 使用方法

### C++中使用

```cpp
// 解析包含多个多边形的GeoJSON数据
TArray<FSimpleGeoJsonItem> Result = AYunnToPolygon::ParseGeoJsonToSimpleArray(
    YourGeoJsonString,
    TEXT("owner"),
    TEXT("remark"),
    TEXT("serial_number")
);

// 处理拆分后的结果
for (int32 i = 0; i < Result.Num(); i++)
{
    const auto& Item = Result[i];
    
    // 检查是否为拆分的多边形
    if (Item.Property1.Contains(TEXT("*")))
    {
        FString OriginalOwner, PolygonIndex;
        Item.Property1.Split(TEXT("*"), &OriginalOwner, &PolygonIndex);
        
        UE_LOG(LogTemp, Log, TEXT("拆分的多边形 - 原始业主: %s, 多边形索引: %s"), 
            *OriginalOwner, *PolygonIndex);
    }
    
    // 使用坐标数据创建独立的多边形
    CreateIndependentPolygon(Item.Coordinates, Item.Property1);
}
```

### 蓝图中使用

1. 调用 **"Parse Geo Json To Simple Array"** 节点
2. 输入GeoJSON字符串和属性键名
3. 遍历返回的数组，每个元素代表一个独立的多边形
4. 通过Property1中的 `*索引` 识别拆分的多边形

## 📋 实际应用示例

### 处理复杂地块数据

```cpp
void ProcessComplexLandData(const FString& GeoJsonFilePath)
{
    // 读取并解析GeoJSON文件
    TArray<FSimpleGeoJsonItem> AllPolygons = AYunnToPolygon::ParseGeoJsonFileToSimpleArray(
        GeoJsonFilePath,
        TEXT("owner"),
        TEXT("remark"),
        TEXT("serial_number")
    );
    
    UE_LOG(LogTemp, Warning, TEXT("解析完成，共获得 %d 个独立多边形"), AllPolygons.Num());
    
    // 统计拆分情况
    int32 OriginalFeatures = 0;
    int32 SplitPolygons = 0;
    TMap<FString, int32> OwnerPolygonCount;
    
    for (const auto& Polygon : AllPolygons)
    {
        if (Polygon.Property1.Contains(TEXT("*")))
        {
            // 这是拆分出来的多边形
            FString OriginalOwner, PolygonIndex;
            Polygon.Property1.Split(TEXT("*"), &OriginalOwner, &PolygonIndex);
            
            // 统计每个业主的多边形数量
            int32* Count = OwnerPolygonCount.Find(OriginalOwner);
            if (Count)
            {
                (*Count)++;
            }
            else
            {
                OwnerPolygonCount.Add(OriginalOwner, 1);
                OriginalFeatures++;
            }
            SplitPolygons++;
        }
    }
    
    UE_LOG(LogTemp, Warning, TEXT("=== 拆分统计 ==="));
    UE_LOG(LogTemp, Warning, TEXT("原始Feature数量: %d"), OriginalFeatures);
    UE_LOG(LogTemp, Warning, TEXT("拆分后多边形数量: %d"), SplitPolygons);
    
    // 显示每个业主的多边形分布
    for (const auto& OwnerCount : OwnerPolygonCount)
    {
        UE_LOG(LogTemp, Warning, TEXT("%s: %d 个多边形"), *OwnerCount.Key, OwnerCount.Value);
    }
    
    // 为每个独立多边形创建可视化
    for (int32 i = 0; i < AllPolygons.Num(); i++)
    {
        const auto& Polygon = AllPolygons[i];
        CreatePolygonVisualization(Polygon, i);
    }
}

void CreatePolygonVisualization(const FSimpleGeoJsonItem& Polygon, int32 Index)
{
    // 解析Property1获取原始信息和索引
    FString DisplayName = Polygon.Property1;
    FLinearColor PolygonColor = FLinearColor::Blue;
    
    if (Polygon.Property1.Contains(TEXT("*")))
    {
        FString OriginalOwner, PolygonIndexStr;
        Polygon.Property1.Split(TEXT("*"), &OriginalOwner, &PolygonIndexStr);
        
        // 根据多边形索引设置不同颜色
        int32 PolygonIndex = FCString::Atoi(*PolygonIndexStr);
        switch (PolygonIndex % 4)
        {
            case 0: PolygonColor = FLinearColor::Red; break;
            case 1: PolygonColor = FLinearColor::Green; break;
            case 2: PolygonColor = FLinearColor::Blue; break;
            case 3: PolygonColor = FLinearColor::Yellow; break;
        }
        
        DisplayName = FString::Printf(TEXT("%s (第%d块)"), *OriginalOwner, PolygonIndex + 1);
    }
    
    // 创建多边形标签
    FString PolygonLabel = FString::Printf(TEXT("%s\n类型: %s\n编号: %s"), 
        *DisplayName, *Polygon.Property2, *Polygon.Property3);
    
    // 使用坐标创建独立的多边形网格
    CreateIndependentPolygonMesh(Polygon.Coordinates, PolygonColor, PolygonLabel);
}
```

## 🧪 测试功能

### 运行拆分测试
- **蓝图**: 调用 "Test Polygon Splitting Parsing"
- **C++**: `UBPFunctionLibBPLibrary::TestPolygonSplittingParsing()`

### 预期测试输出
```
=== 开始测试多边形拆分功能 ===
测试数据: 包含2个独立多边形的Feature
=== 拆分结果验证 ===
预期结果: 2个独立的结构体项
实际结果: 2 个结构体项
✅ 拆分成功！
--- 第一个多边形 ---
Property1: 广州铁路中心医院*0 (预期: 广州铁路中心医院*0)
Property2: 国有 (预期: 国有)
Property3: 268 (预期: 268)
坐标点数量: 4
--- 第二个多边形 ---
Property1: 广州铁路中心医院*1 (预期: 广州铁路中心医院*1)
Property2: 国有 (预期: 国有)
Property3: 268 (预期: 268)
坐标点数量: 4
Property1格式验证: ✅ 正确
Property2和Property3保持不变: ✅ 正确
=== 多边形拆分功能测试完成 ===
```

## ⚠️ 重要说明

### 拆分规则
1. **索引从0开始**: 第一个多边形标记为 `*0`，第二个为 `*1`，以此类推
2. **只修改Property1**: Property2和Property3保持原始值不变
3. **独立坐标**: 每个拆分项只包含对应多边形的坐标点
4. **保持顺序**: 拆分后的项目顺序与原始MultiPolygon中的顺序一致

### 兼容性
- ✅ **单个多边形**: 仍然正常工作，会添加 `*0` 后缀
- ✅ **多个多边形**: 自动拆分为多个独立项
- ✅ **FeatureCollection**: 每个Feature独立处理拆分
- ✅ **现有代码**: 不影响现有的使用方式

### 性能考虑
- **内存使用**: 拆分会增加结构体数量，但减少了嵌套复杂度
- **处理效率**: 每个多边形独立处理，便于并行化
- **可视化友好**: 直接支持UE5的独立多边形渲染

## 🎯 解决的问题

✅ **UE5兼容性**: 解决了UE5不支持复杂多边形结构的问题
✅ **独立处理**: 每个多边形可以独立创建、修改、删除
✅ **清晰标识**: 通过Property1的索引后缀清楚区分不同多边形
✅ **数据完整**: 保持了原始的属性信息和坐标精度

现在您可以完美处理包含多个独立多边形的复杂地块数据了！🎉
