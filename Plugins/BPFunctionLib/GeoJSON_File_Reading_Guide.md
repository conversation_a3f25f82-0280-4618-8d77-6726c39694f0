# GeoJSON 文件读取功能使用指南

## 🎯 功能概述

现在您有两种方式来解析GeoJSON数据：

1. **`ParseGeoJsonToSimpleArray`** - 传入GeoJSON字符串内容
2. **`ParseGeoJsonFileToSimpleArray`** - 传入本地文件路径（新增）

## 🚀 新增文件读取功能

### 核心函数
```cpp
UFUNCTION(BlueprintCallable, Category = "YunnToPolygon")
static TArray<FSimpleGeoJsonItem> ParseGeoJsonFileToSimpleArray(
    const FString& FilePath,        // 本地GeoJSON文件路径
    const FString& PropertyKey1,    // 第一个属性键
    const FString& PropertyKey2,    // 第二个属性键
    const FString& PropertyKey3     // 第三个属性键
);
```

### 功能特性
- ✅ **自动文件读取** - 内部处理文件读取和编码转换
- ✅ **路径验证** - 检查文件是否存在
- ✅ **错误处理** - 详细的错误日志输出
- ✅ **内容验证** - 检查文件内容是否为空
- ✅ **统计信息** - 显示文件大小和解析结果数量

## 📁 使用方法

### C++中使用

```cpp
// 方法1: 直接传入文件路径（推荐）
TArray<FSimpleGeoJsonItem> LandData = AYunnToPolygon::ParseGeoJsonFileToSimpleArray(
    TEXT("C:/Data/land_ownership.geojson"),  // 您的GeoJSON文件路径
    TEXT("owner"),                           // 土地所有者
    TEXT("remark"),                          // 土地类型
    TEXT("serial_number")                    // 序列号
);

// 方法2: 如果您已经有字符串内容
FString GeoJsonContent = TEXT("您的GeoJSON内容");
TArray<FSimpleGeoJsonItem> LandData = AYunnToPolygon::ParseGeoJsonToSimpleArray(
    GeoJsonContent,
    TEXT("owner"),
    TEXT("remark"),
    TEXT("serial_number")
);

// 处理解析结果
for (const auto& Land : LandData)
{
    FString Owner = Land.Property1;           // 土地所有者
    FString LandType = Land.Property2;        // 土地类型
    FString SerialNumber = Land.Property3;    // 序列号
    TArray<FVector2D> Coordinates = Land.Coordinates;  // 所有坐标点
    
    // 处理每个地块的数据...
}
```

### 蓝图中使用

1. **使用文件路径**:
   - 搜索 **"Parse Geo Json File To Simple Array"** 节点
   - 输入文件路径和三个属性键名

2. **使用字符串内容**:
   - 搜索 **"Parse Geo Json To Simple Array"** 节点
   - 输入GeoJSON字符串和三个属性键名

## 📋 实际应用示例

### 完整的土地数据处理流程

```cpp
void ProcessLandOwnershipData()
{
    // 设置您的GeoJSON文件路径
    FString GeoJsonFilePath = TEXT("D:/Projects/Data/land_ownership.geojson");
    
    UE_LOG(LogTemp, Warning, TEXT("开始处理土地所有权数据..."));
    
    // 解析文件
    TArray<FSimpleGeoJsonItem> AllLands = AYunnToPolygon::ParseGeoJsonFileToSimpleArray(
        GeoJsonFilePath,
        TEXT("owner"),              // 土地所有者
        TEXT("remark"),             // 土地类型（集体/国有）
        TEXT("serial_number")       // 序列号
    );
    
    if (AllLands.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("未能解析到任何土地数据，请检查文件路径和格式"));
        return;
    }
    
    UE_LOG(LogTemp, Warning, TEXT("成功解析 %d 块土地数据"), AllLands.Num());
    
    // 数据统计
    int32 CollectiveLands = 0;  // 集体土地
    int32 StateLands = 0;       // 国有土地
    int32 OtherLands = 0;       // 其他类型
    
    // 处理每块土地
    for (int32 i = 0; i < AllLands.Num(); i++)
    {
        const auto& Land = AllLands[i];
        
        // 统计土地类型
        if (Land.Property2 == TEXT("集体"))
        {
            CollectiveLands++;
        }
        else if (Land.Property2 == TEXT("国有"))
        {
            StateLands++;
        }
        else
        {
            OtherLands++;
        }
        
        // 创建土地可视化
        CreateLandVisualization(Land, i);
        
        // 记录详细信息
        UE_LOG(LogTemp, Log, TEXT("地块 %d: %s (%s) - 坐标点: %d"), 
            i, *Land.Property1, *Land.Property2, Land.Coordinates.Num());
    }
    
    // 输出统计结果
    UE_LOG(LogTemp, Warning, TEXT("=== 土地数据统计 ==="));
    UE_LOG(LogTemp, Warning, TEXT("集体土地: %d 块"), CollectiveLands);
    UE_LOG(LogTemp, Warning, TEXT("国有土地: %d 块"), StateLands);
    UE_LOG(LogTemp, Warning, TEXT("其他土地: %d 块"), OtherLands);
    UE_LOG(LogTemp, Warning, TEXT("总计: %d 块"), AllLands.Num());
}

void CreateLandVisualization(const FSimpleGeoJsonItem& Land, int32 Index)
{
    // 根据土地类型设置颜色
    FLinearColor LandColor;
    if (Land.Property2 == TEXT("集体"))
    {
        LandColor = FLinearColor::Green;      // 集体土地 - 绿色
    }
    else if (Land.Property2 == TEXT("国有"))
    {
        LandColor = FLinearColor::Blue;       // 国有土地 - 蓝色
    }
    else
    {
        LandColor = FLinearColor::Gray;       // 其他类型 - 灰色
    }
    
    // 创建土地标签
    FString LandLabel = FString::Printf(TEXT("地块 %d\n所有者: %s\n类型: %s\n编号: %s"), 
        Index, *Land.Property1, *Land.Property2, *Land.Property3);
    
    // 使用坐标创建3D多边形网格
    // CreatePolygonMeshFromCoordinates(Land.Coordinates, LandColor, LandLabel);
    
    // 或者创建边界线
    // CreateBoundaryLines(Land.Coordinates, LandColor);
}
```

### 错误处理和调试

```cpp
void SafeProcessGeoJsonFile(const FString& FilePath)
{
    // 检查文件扩展名
    if (!FilePath.EndsWith(TEXT(".geojson")) && !FilePath.EndsWith(TEXT(".json")))
    {
        UE_LOG(LogTemp, Warning, TEXT("文件扩展名不是.geojson或.json: %s"), *FilePath);
    }
    
    // 尝试解析
    TArray<FSimpleGeoJsonItem> Result = AYunnToPolygon::ParseGeoJsonFileToSimpleArray(
        FilePath,
        TEXT("owner"),
        TEXT("remark"),
        TEXT("serial_number")
    );
    
    // 检查结果
    if (Result.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("解析失败，可能的原因："));
        UE_LOG(LogTemp, Error, TEXT("1. 文件路径错误: %s"), *FilePath);
        UE_LOG(LogTemp, Error, TEXT("2. 文件格式不是有效的GeoJSON"));
        UE_LOG(LogTemp, Error, TEXT("3. 属性键名不匹配"));
        UE_LOG(LogTemp, Error, TEXT("4. 几何类型不是MultiPolygon"));
        return;
    }
    
    // 验证数据质量
    for (int32 i = 0; i < Result.Num(); i++)
    {
        const auto& Item = Result[i];
        
        if (Item.Coordinates.Num() == 0)
        {
            UE_LOG(LogTemp, Warning, TEXT("地块 %d 没有坐标数据"), i);
        }
        
        if (Item.Property1.IsEmpty() || Item.Property2.IsEmpty() || Item.Property3.IsEmpty())
        {
            UE_LOG(LogTemp, Warning, TEXT("地块 %d 缺少属性数据"), i);
        }
    }
    
    UE_LOG(LogTemp, Warning, TEXT("数据验证完成，共 %d 个有效地块"), Result.Num());
}
```

## 🔧 测试功能

### 运行测试
在蓝图中调用 **"Test Geo Json File Reading"** 函数来测试文件读取功能。

**注意**: 测试函数中的文件路径是 `C:/Data/land_ownership.geojson`，请根据您的实际文件位置修改测试函数中的路径。

### 修改测试路径
如果需要修改测试路径，可以在 `BPFunctionLibBPLibrary.cpp` 中找到 `TestGeoJsonFileReading` 函数，修改这一行：
```cpp
FString TestFilePath = TEXT("您的实际文件路径");
```

## 📁 支持的文件路径格式

- ✅ **绝对路径**: `C:/Data/land_ownership.geojson`
- ✅ **相对路径**: `../Data/land_ownership.geojson`
- ✅ **UE5项目路径**: `Content/Data/land_ownership.geojson`
- ✅ **网络路径**: `//Server/Share/data.geojson`

## ⚠️ 注意事项

1. **文件编码**: 支持UTF-8编码的GeoJSON文件
2. **文件大小**: 大文件可能需要较长的加载时间
3. **路径分隔符**: 建议使用正斜杠 `/` 或双反斜杠 `\\`
4. **权限**: 确保UE5有读取文件的权限

## 🎯 优势对比

**新方法（文件路径）**:
- ✅ 简单易用，直接传入文件路径
- ✅ 自动处理文件读取和错误检查
- ✅ 适合处理大文件
- ✅ 减少内存占用

**原方法（字符串内容）**:
- ✅ 适合动态生成的GeoJSON内容
- ✅ 适合网络传输的数据
- ✅ 可以预处理数据内容

现在您可以直接传入GeoJSON文件路径，让函数自动处理文件读取和解析！
