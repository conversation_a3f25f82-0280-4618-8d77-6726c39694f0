# GeoJSON MultiPolygon 解析函数使用说明

## 功能概述

`AYunnToPolygon::ParseGeoJsonMultiPolygon` 函数用于解析标准GeoJSON格式的MultiPolygon数据，提取指定的properties属性值，并将geometry中的coordinates转换为UE5可用的FVector2D数组。

## 函数签名

```cpp
UFUNCTION(BlueprintCallable, Category = "YunnToPolygon")
static FGeoJsonParseResult ParseGeoJsonMultiPolygon(
    const FString& GeoJsonString,
    const FString& PropertyKey1,
    const FString& PropertyKey2,
    const FString& PropertyKey3
);
```

## 参数说明

- `GeoJsonString`: 标准GeoJSON格式的字符串
- `PropertyKey1`, `PropertyKey2`, `PropertyKey3`: 要从properties中提取的三个自定义属性的键名

## 返回值结构

```cpp
USTRUCT(BlueprintType)
struct FGeoJsonParseResult
{
    UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
    FString Property1;  // 第一个自定义属性值

    UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
    FString Property2;  // 第二个自定义属性值

    UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
    FString Property3;  // 第三个自定义属性值

    // MultiPolygon的坐标数据，三维数组结构：
    // [多边形索引][环索引][点索引]
    UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
    TArray<TArray<TArray<FVector2D>>> Coordinates;
};
```

## 坐标数据结构说明

MultiPolygon的坐标数据是一个三维数组：
- **第一维**: 多边形索引 - 一个MultiPolygon可以包含多个独立的多边形
- **第二维**: 环索引 - 每个多边形可以有多个环（第一个是外环，其余是内环/洞）
- **第三维**: 点索引 - 每个环由多个坐标点组成

## 示例GeoJSON数据

```json
{
  "type": "Feature",
  "properties": {
    "name": "示例区域",
    "area_code": "001",
    "population": "10000"
  },
  "geometry": {
    "type": "MultiPolygon",
    "coordinates": [
      [
        [
          [116.3974, 39.9093],
          [116.3984, 39.9093],
          [116.3984, 39.9103],
          [116.3974, 39.9103],
          [116.3974, 39.9093]
        ]
      ]
    ]
  }
}
```

## 使用示例（C++）

```cpp
// 准备GeoJSON字符串
FString GeoJsonData = TEXT("{\"type\":\"Feature\",\"properties\":{\"name\":\"示例区域\",\"area_code\":\"001\",\"population\":\"10000\"},\"geometry\":{\"type\":\"MultiPolygon\",\"coordinates\":[[[[116.3974,39.9093],[116.3984,39.9093],[116.3984,39.9103],[116.3974,39.9103],[116.3974,39.9093]]]]}}");

// 调用解析函数
FGeoJsonParseResult Result = AYunnToPolygon::ParseGeoJsonMultiPolygon(
    GeoJsonData,
    TEXT("name"),
    TEXT("area_code"), 
    TEXT("population")
);

// 使用解析结果
UE_LOG(LogTemp, Log, TEXT("Name: %s"), *Result.Property1);
UE_LOG(LogTemp, Log, TEXT("Area Code: %s"), *Result.Property2);
UE_LOG(LogTemp, Log, TEXT("Population: %s"), *Result.Property3);

// 遍历坐标数据
for (int32 PolygonIndex = 0; PolygonIndex < Result.Coordinates.Num(); PolygonIndex++)
{
    const auto& Polygon = Result.Coordinates[PolygonIndex];
    UE_LOG(LogTemp, Log, TEXT("Polygon %d has %d rings"), PolygonIndex, Polygon.Num());
    
    for (int32 RingIndex = 0; RingIndex < Polygon.Num(); RingIndex++)
    {
        const auto& Ring = Polygon[RingIndex];
        UE_LOG(LogTemp, Log, TEXT("Ring %d has %d points"), RingIndex, Ring.Num());
        
        for (int32 PointIndex = 0; PointIndex < Ring.Num(); PointIndex++)
        {
            const FVector2D& Point = Ring[PointIndex];
            UE_LOG(LogTemp, Log, TEXT("Point %d: (%.6f, %.6f)"), PointIndex, Point.X, Point.Y);
        }
    }
}
```

## 使用示例（蓝图）

1. 在蓝图中调用 `Parse Geo Json Multi Polygon` 节点
2. 输入GeoJSON字符串和三个属性键名
3. 从返回的结构体中获取解析后的属性值和坐标数据
4. 可以使用坐标数据创建多边形网格或进行其他几何操作

## 注意事项

1. 函数只支持MultiPolygon类型的geometry，其他类型（Point、LineString、Polygon等）会返回错误
2. 如果指定的属性键在properties中不存在，对应的属性值将为空字符串
3. 坐标数据保持原始的经纬度格式，如需转换为UE5世界坐标需要额外处理
4. 函数会进行基本的JSON格式验证，无效的JSON会返回空结果并输出错误日志

## 错误处理

函数包含以下错误检查：
- JSON格式验证
- Feature类型验证
- MultiPolygon类型验证
- 必要字段存在性验证

所有错误都会通过UE_LOG输出到日志中，便于调试。
