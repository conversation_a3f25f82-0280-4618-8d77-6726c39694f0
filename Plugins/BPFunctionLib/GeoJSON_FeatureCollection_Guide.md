# GeoJSON FeatureCollection 解析功能使用指南

## 🎯 功能概述

现在您有两个函数可以使用：

1. **`ParseGeoJsonMultiPolygon`** - 解析单个Feature
2. **`ParseGeoJsonFeatureCollection`** - 解析包含多个Feature的FeatureCollection

## 📊 数据结构

### 单个Feature结果
```cpp
USTRUCT(BlueprintType)
struct FGeoJsonFeature
{
    UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
    FString Property1, Property2, Property3;  // 自定义属性值
    
    UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
    TArray<FGeoJsonPolygon> Polygons;  // 多边形数据
};
```

### FeatureCollection结果
```cpp
USTRUCT(BlueprintType)
struct FGeoJsonCollectionResult
{
    UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
    TArray<FGeoJsonFeature> Features;  // 所有Feature数据
    
    UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
    int32 TotalFeatures;      // 总Feature数量
    
    UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
    int32 SuccessfulFeatures; // 成功解析数量
    
    UPROPERTY(BlueprintReadWrite, Category = "GeoJSON")
    int32 FailedFeatures;     // 解析失败数量
};
```

## 🚀 使用方法

### 方法1: 解析FeatureCollection（推荐）

**C++使用**:
```cpp
// 读取您的GeoJSON文件内容到FString
FString GeoJsonContent = TEXT("您的FeatureCollection内容");

// 调用解析函数
FGeoJsonCollectionResult Result = AYunnToPolygon::ParseGeoJsonFeatureCollection(
    GeoJsonContent,
    TEXT("name"),        // 第一个属性键
    TEXT("area_code"),   // 第二个属性键
    TEXT("population")   // 第三个属性键
);

// 检查解析结果
UE_LOG(LogTemp, Warning, TEXT("总共解析了 %d 个Feature，成功 %d 个"), 
    Result.TotalFeatures, Result.SuccessfulFeatures);

// 遍历所有Feature
for (const auto& Feature : Result.Features)
{
    UE_LOG(LogTemp, Log, TEXT("Feature: %s, 区域代码: %s, 人口: %s"), 
        *Feature.Property1, *Feature.Property2, *Feature.Property3);
    
    // 遍历每个Feature的多边形
    for (const auto& Polygon : Feature.Polygons)
    {
        UE_LOG(LogTemp, Log, TEXT("  多边形 %d 有 %d 个环"), 
            Polygon.PolygonIndex, Polygon.Rings.Num());
        
        // 遍历每个多边形的环
        for (const auto& Ring : Polygon.Rings)
        {
            UE_LOG(LogTemp, Log, TEXT("    环 %d (%s) 有 %d 个点"), 
                Ring.RingIndex, 
                Ring.RingIndex == 0 ? TEXT("外环") : TEXT("内环"),
                Ring.Points.Num());
            
            // 使用坐标点
            for (const FVector2D& Point : Ring.Points)
            {
                // Point.X = 经度, Point.Y = 纬度
                // 在这里处理每个坐标点
            }
        }
    }
}
```

**蓝图使用**:
1. 搜索并添加 **"Parse Geo Json Feature Collection"** 节点
2. 输入GeoJSON字符串和三个属性键名
3. 从返回的 `FGeoJsonCollectionResult` 中获取 `Features` 数组
4. 遍历每个Feature获取属性和坐标数据

### 方法2: 解析单个Feature

如果您的文件只包含一个Feature，可以使用原有函数：

```cpp
FGeoJsonParseResult Result = AYunnToPolygon::ParseGeoJsonMultiPolygon(
    GeoJsonContent, PropertyKey1, PropertyKey2, PropertyKey3);
```

## 📁 支持的GeoJSON格式

### FeatureCollection格式
```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "properties": {
        "name": "区域1",
        "area_code": "001",
        "population": "10000"
      },
      "geometry": {
        "type": "MultiPolygon",
        "coordinates": [...]
      }
    },
    {
      "type": "Feature",
      "properties": {
        "name": "区域2",
        "area_code": "002", 
        "population": "20000"
      },
      "geometry": {
        "type": "MultiPolygon",
        "coordinates": [...]
      }
    }
  ]
}
```

### 单个Feature格式
```json
{
  "type": "Feature",
  "properties": {
    "name": "区域名称",
    "area_code": "001",
    "population": "10000"
  },
  "geometry": {
    "type": "MultiPolygon",
    "coordinates": [...]
  }
}
```

## 🔧 测试功能

### 测试FeatureCollection解析
在蓝图中调用 **"Test Geo Json Feature Collection Parsing"** 函数，查看输出日志验证功能。

### 测试单个Feature解析
在蓝图中调用 **"Test Geo Json Parsing"** 函数。

## ⚠️ 注意事项

1. **几何类型限制**: 只支持MultiPolygon类型，其他几何类型会被跳过
2. **属性容错**: 如果指定的属性键不存在，对应值将为空字符串
3. **错误处理**: 解析失败的Feature会被跳过，不会中断整个解析过程
4. **日志输出**: 所有解析过程都有详细的日志输出，便于调试

## 📈 性能考虑

- **大文件处理**: 函数可以处理包含大量Feature的文件
- **内存使用**: 所有数据会加载到内存中，注意文件大小
- **解析统计**: 通过返回的统计信息监控解析成功率

## 🎯 实际应用示例

```cpp
// 从文件读取GeoJSON
FString FilePath = TEXT("C:/Data/regions.geojson");
FString GeoJsonContent;
if (FFileHelper::LoadFileToString(GeoJsonContent, *FilePath))
{
    // 解析所有区域数据
    FGeoJsonCollectionResult Result = AYunnToPolygon::ParseGeoJsonFeatureCollection(
        GeoJsonContent,
        TEXT("name"),
        TEXT("code"), 
        TEXT("area")
    );
    
    // 为每个区域创建3D网格
    for (const auto& Feature : Result.Features)
    {
        // 使用Feature.Property1作为区域名称
        // 使用Feature.Polygons创建多边形网格
        CreateRegionMesh(Feature);
    }
}
```

现在您可以轻松处理包含多个Feature的GeoJSON文件了！
