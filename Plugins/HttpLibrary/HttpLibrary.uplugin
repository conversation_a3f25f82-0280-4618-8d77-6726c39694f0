{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "HTTP Library", "Description": "Send HTTP requests using blueprints.", "Category": "Messaging", "CreatedBy": "Tracer Interactive", "CreatedByURL": "https://tracerinteractive.com", "DocsURL": "https://cdn.tracerinteractive.com/httplibrary/documentation.pdf", "MarketplaceURL": "", "SupportURL": "", "EngineVersion": "5.2.0", "CanContainContent": false, "Installed": true, "Modules": [{"Name": "HttpLibrary", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "WhitelistPlatforms": ["Win64", "<PERSON>", "Linux", "Android", "IOS"]}, {"Name": "HttpLibraryBlueprintSupport", "Type": "UncookedOnly", "LoadingPhase": "PostConfigInit", "WhitelistPlatforms": ["Win64", "<PERSON>", "Linux"]}], "Plugins": [{"Name": "JsonLibrary", "Enabled": true}]}