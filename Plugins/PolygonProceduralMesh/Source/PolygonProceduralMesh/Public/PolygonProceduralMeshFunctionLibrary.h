// Copyright 2023 <PERSON><PERSON><PERSON><PERSON>. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "PolygonProceduralMeshFunctionLibrary.generated.h"


/**
 * 
 */
UCLASS()
class POLYGONPROCEDURALMESH_API UPolygonProceduralMeshFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	/**
	 * Automatically generate triangles for a polygon mesh. Vertices must have at least 3 elements.
	 *
	 * @param Vertices			Polygon's vertices (MUST BE IN A COUNTER-CLOCKWISE ORDER!)
	 * @param Triangles			Output array that contains calculated triangles
	 * @param bTopOriented		Whether a polygon should be top or bottom oriented
	 */
	UFUNCTION(BlueprintCallable, Category = "Components|ProceduralMesh|PolygonProceduralMesh")
	static void CalculateTrianglesForPolygonMesh(const TArray<FVector>& Vertices, TArray<int32>& Triangles, bool bTopOriented = true);

	/**
	 * Automatically generate UVs for a polygon mesh. Vertices must have at least 3 elements.
	 *
	 * @param Vertices			Polygon's vertices (MUST BE IN A COUNTER-CLOCKWISE ORDER!)
	 * @param UVs				Output array that contains calculated UVs
	 */
	UFUNCTION(BlueprintCallable, Category = "Components|ProceduralMesh|PolygonProceduralMesh")
	static void CalculateUVsForPolygonMesh(const TArray<FVector>& Vertices, TArray<FVector2D>& UVs);

	/**
	 * Sorts vertices so it'll be possible to generate a polygon mesh from them. Vertices must have at least 3 elements.
	 */
	UFUNCTION(BlueprintCallable, Category = "Components|ProceduralMesh|PolygonProceduralMesh")
	static void SortVerticesForPolygonMesh(UPARAM(ref) TArray<FVector>& Vertices);
};
