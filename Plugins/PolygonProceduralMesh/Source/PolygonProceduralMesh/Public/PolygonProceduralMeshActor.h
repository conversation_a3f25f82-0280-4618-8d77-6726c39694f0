// Copyright 2023 <PERSON><PERSON><PERSON><PERSON>. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "PolygonProceduralMeshActor.generated.h"

class UProceduralMeshComponent;

UCLASS()
class POLYGONPROCEDURALMESH_API APolygonProceduralMeshActor : public AActor
{
	GENERATED_UCLASS_BODY()

private:
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components", meta = (AllowPrivateAccess = "true"))
	TObjectPtr<UProceduralMeshComponent> ProceduralMeshComponent;

public:
	/** If true, PolygonVertices array will be automatically sorted, but the shape generation might be less precise.
	 * Disable it if you want to have more control in generating a polygon, but YOU HAVE TO MANUALLY SORT POLYGON VERTICES IN THE COUNTER-CLOCKWISE ORDER in that case. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PolygonProceduralMesh")
	bool bAutomaticallySortPolygonVertices;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PolygonProceduralMesh", meta = (MakeEditWidget = true))
	TArray<FVector> PolygonVertices;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PolygonProceduralMesh", meta = (ClampMin = 0.0))
	double Height;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PolygonProceduralMesh")
	bool bCreateBottom;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PolygonProceduralMesh")
	bool bCreateCollision;

	UFUNCTION(BlueprintCallable, Category = "PolygonProceduralMesh")
	void GenerateMesh();

	//~ Begin AActor Interface
	virtual void OnConstruction(const FTransform& Transform) override;
	//~ End AActor Interface

	bool HasHeight() const;
	
	FORCEINLINE UProceduralMeshComponent* GetProceduralMeshComponent() const { return ProceduralMeshComponent; }
};
