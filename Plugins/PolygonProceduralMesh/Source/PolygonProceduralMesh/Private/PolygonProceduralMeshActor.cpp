// Copyright 2023 <PERSON><PERSON><PERSON><PERSON>. All Rights Reserved.


#include "PolygonProceduralMeshActor.h"
#include "ProceduralMeshComponent.h"
#include "KismetProceduralMeshLibrary.h"
#include "PolygonProceduralMeshFunctionLibrary.h"

APolygonProceduralMeshActor::APolygonProceduralMeshActor(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
	, bAutomaticallySortPolygonVertices(true)
	, bCreateCollision(true)
{
	ProceduralMeshComponent = ObjectInitializer.CreateDefaultSubobject<UProceduralMeshComponent>(this, TEXT("ProceduralMeshComponent"));
	ProceduralMeshComponent->SetMobility(EComponentMobility::Static);
	SetRootComponent(ProceduralMeshComponent.Get());
}

void APolygonProceduralMeshActor::GenerateMesh()
{
	ProceduralMeshComponent->ClearCollisionConvexMeshes();
	ProceduralMeshComponent->ClearAllMeshSections();

	TArray<FVector> Vertices;
	for (const FVector& Point : PolygonVertices)
	{
		Vertices.AddUnique(Point);
	}
	
	if (Vertices.Num() < 3)
	{
		return;
	}

	if (bAutomaticallySortPolygonVertices)
	{
		UPolygonProceduralMeshFunctionLibrary::SortVerticesForPolygonMesh(Vertices);
	}

	TArray<int32> Triangles;
	UPolygonProceduralMeshFunctionLibrary::CalculateTrianglesForPolygonMesh(Vertices, Triangles);

	TArray<FVector2D> UVs;
	UPolygonProceduralMeshFunctionLibrary::CalculateUVsForPolygonMesh(Vertices, UVs);
	
	const bool bDoubleSidedPolygon = bCreateBottom && !HasHeight();

	if (bDoubleSidedPolygon)
	{
		TArray<int32> BottomTriangles;
		UPolygonProceduralMeshFunctionLibrary::CalculateTrianglesForPolygonMesh(Vertices, BottomTriangles, false);
		Triangles.Append(BottomTriangles);
	}

	const TArray<FLinearColor> VertexColors;
	TArray<TArray<FVector>> ConvexMeshes;

	auto CreateMeshSection = [this, &VertexColors, &ConvexMeshes](const TArray<FVector>& InVertices, const TArray<int32>& InTriangles, const TArray<FVector2D>& InUVs)
	{
		TArray<FVector> Normals;
		TArray<FProcMeshTangent> Tangents;
		UKismetProceduralMeshLibrary::CalculateTangentsForMesh(InVertices, InTriangles, InUVs, Normals, Tangents);
		
		ProceduralMeshComponent->CreateMeshSection_LinearColor(ProceduralMeshComponent->GetNumSections(), InVertices, InTriangles, Normals, InUVs, VertexColors, Tangents, bCreateCollision);

		if (bCreateCollision)
		{
			if (ConvexMeshes.IsEmpty())
			{
				ConvexMeshes.Add(InVertices);
			}
			else
			{
				ConvexMeshes[0].Append(InVertices);
			}
		}
	};
	
	CreateMeshSection(Vertices, Triangles, UVs);

	const int32 VerticesNum = Vertices.Num();
	
	const bool IsProperTrianglesAmount = Triangles.Num() == ((VerticesNum - 2) * 3);
	const bool bCanCalculateSideTriangles = HasHeight() && IsProperTrianglesAmount;
	
	if (!bDoubleSidedPolygon && bCanCalculateSideTriangles)
	{
		TArray<FVector> BottomVertices;
		
		for (int32 i = 0; i < VerticesNum; ++i)
		{
			TArray<FVector> SideVertices;
			TArray<FVector2D> SideUVs;
			TArray<int32> SideTriangles;
			
			const int32 Vert0 = i;
			const int32 Vert1 = (i + 1) % VerticesNum;

			const FVector& CurrentVertex = Vertices[Vert0];
			const FVector& NextVertex = Vertices[Vert1];

			const FVector CurrentVertexWithOffset = FVector(CurrentVertex.X, CurrentVertex.Y, CurrentVertex.Z - Height);
			const FVector NextVertexWithOffset = FVector(NextVertex.X, NextVertex.Y, NextVertex.Z - Height);

			if (bCreateBottom)
			{
				BottomVertices.Add(CurrentVertexWithOffset);
			}
			
			SideVertices.Add(CurrentVertex);
			SideVertices.Add(NextVertex);
			SideVertices.Add(NextVertexWithOffset);
			SideVertices.Add(CurrentVertexWithOffset);

			SideUVs.Add(FVector2D(1.0, 1.0));
			SideUVs.Add(FVector2D(0.0, 1.0));
			SideUVs.Add(FVector2D(0.0, 0.0));
			SideUVs.Add(FVector2D(1.0, 0.0));

			UPolygonProceduralMeshFunctionLibrary::CalculateTrianglesForPolygonMesh(SideVertices, SideTriangles, false);
			
			CreateMeshSection(SideVertices, SideTriangles, SideUVs);
		}

		if (!BottomVertices.IsEmpty())
		{
			TArray<int32> BottomTriangles;
			UPolygonProceduralMeshFunctionLibrary::CalculateTrianglesForPolygonMesh(BottomVertices, BottomTriangles, false);

			TArray<FVector2D> BottomUVs;
			UPolygonProceduralMeshFunctionLibrary::CalculateUVsForPolygonMesh(BottomVertices, BottomUVs);

			CreateMeshSection(BottomVertices, BottomTriangles, BottomUVs);
		}
	}

	if (bCreateCollision)
	{
		ProceduralMeshComponent->SetCollisionConvexMeshes(ConvexMeshes);
	}
}

void APolygonProceduralMeshActor::OnConstruction(const FTransform& Transform)
{
	GenerateMesh();
	
	Super::OnConstruction(Transform);
}

bool APolygonProceduralMeshActor::HasHeight() const
{
	return Height > 0.0;
}
