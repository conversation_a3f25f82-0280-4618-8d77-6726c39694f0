// Copyright 2023 <PERSON><PERSON><PERSON><PERSON>. All Rights Reserved.


#include "PolygonProceduralMeshFunctionLibrary.h"
#include "CompGeom/PolygonTriangulation.h"

void UPolygonProceduralMeshFunctionLibrary::CalculateTrianglesForPolygonMesh(const TArray<FVector>& Vertices, TArray<int32>& Triangles, bool bTopOriented)
{
	Triangles.Empty();
	
	if (Vertices.Num() < 3)
	{
		return;
	}

	TArray<UE::Geometry::FIndex3i> TempTriangles;
	PolygonTriangulation::TriangulateSimplePolygon(Vertices, TempTriangles, !bTopOriented);
	for (const UE::Geometry::FIndex3i& Triangle : TempTriangles)
	{
		Triangles.Add(Triangle.A);
		Triangles.Add(Triangle.B);
		Triangles.Add(Triangle.C);
	}
}

void UPolygonProceduralMeshFunctionLibrary::CalculateUVsForPolygonMesh(const TArray<FVector>& Vertices, TArray<FVector2D>& UVs)
{
	UVs.Empty();

	if (Vertices.Num() < 3)
	{
		return;
	}

	const FVector& FirstVertex = Vertices[0];

	double MinX = FirstVertex.X;
	double MaxX = FirstVertex.X;
	double MinY = FirstVertex.Y;
	double MaxY = FirstVertex.Y;

	for (int32 i = 1; i < Vertices.Num(); ++i)
	{
		const FVector& Vertex = Vertices[i];

		MinX = FMath::Min(MinX, Vertex.X);
		MaxX = FMath::Max(MaxX, Vertex.X);
		MinY = FMath::Min(MinY, Vertex.Y);
		MaxY = FMath::Max(MaxY, Vertex.Y);
	}

	auto CalculateActualMax = [](double Max, double Min)->double const
	{
		return FMath::Clamp(Max - Min, DOUBLE_KINDA_SMALL_NUMBER, DOUBLE_BIG_NUMBER);
	};
	
	const double Width = CalculateActualMax(MaxX, MinX);
	const double Height = CalculateActualMax(MaxY, MinY);

	for (const FVector& Vertex : Vertices)
	{
		UVs.Add(FVector2D(Vertex.X / Width, Vertex.Y / Height));
	}
}

void UPolygonProceduralMeshFunctionLibrary::SortVerticesForPolygonMesh(TArray<FVector>& Vertices)
{
	if (Vertices.Num() < 3)
	{
		return;
	}

	FVector PolygonCenter = FVector::ZeroVector;
	for (const FVector& Vertex : Vertices)
	{
		PolygonCenter += Vertex;
	}
	PolygonCenter /= Vertices.Num();

	Vertices.Sort([&PolygonCenter](const FVector& A, const FVector& B)
	{
		const double CAX = A.X - PolygonCenter.X;
		const double CBX = B.X - PolygonCenter.X;
			
		if (CAX >= 0.0 && CBX < 0.0)
		{
			return true;
		}

		if (CAX < 0.0 && CBX >= 0.0)
		{
			return false;
		}

		if (CAX == 0.0 && CBX == 0.0)
		{
			const double CAY = A.Y - PolygonCenter.Y;
			const double CBY = B.Y - PolygonCenter.Y;
				
			if (CAY >= 0.0 || CBY >= 0)
			{
				return A.Y > B.Y;
			}
				
			return B.Y > A.Y;
		}
			
		// Compute the cross product of vectors CA x CB
		const FVector2D CA2 = FVector2D(A - PolygonCenter);
		const FVector2D CB2 = FVector2D(B - PolygonCenter);
		const double CrossProduct2D = FVector2D::CrossProduct(CA2, CB2);

		if (CrossProduct2D < 0.0)
		{
			return true;
		}
			
		if (CrossProduct2D > 0.0)
		{
			return false;
		}

		// Points A and B are on the same line from the center
		// Check which point is closer to the center
		const FVector2D A2 = FVector2D(A);
		const FVector2D B2 = FVector2D(B);
		const FVector2D C2 = FVector2D(PolygonCenter);
			
		const double D1 = FVector2D::DistSquared(A2, C2);
		const double D2 = FVector2D::DistSquared(B2, C2);
			
		return D1 > D2;
	});
}
