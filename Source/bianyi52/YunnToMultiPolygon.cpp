// Fill out your copyright notice in the Description page of Project Settings.


#include "YunnToMultiPolygon.h"

// Sets default values
AYunnToMultiPolygon::AYunnToMultiPolygon()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void AYunnToMultiPolygon::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void AYunnToMultiPolygon::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

void AYunnToMultiPolygon::ExecuteCustomAction()
{
	UE_LOG(LogTemp, Warning, TEXT("12312312312312"));
}

